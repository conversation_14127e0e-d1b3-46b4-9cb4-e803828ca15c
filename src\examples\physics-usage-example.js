/**
 * 物理系统使用示例
 * 展示如何在Cantos MMORPG项目中使用物理系统
 */

import { Vector3, Color3 } from "@babylonjs/core/Maths/math";
import { PhysicsManager, PhysicsUtils, PhysicsBodyPresets } from '../core/physics/index.js';
import { ecsWorld } from '../core/ecs/bitECSSystem.js';

/**
 * 初始化物理系统示例
 * @param {Scene} scene - Babylon.js场景
 * @param {SceneManager} sceneManager - 场景管理器
 * @returns {Object} 示例对象
 */
export async function initializePhysicsExample(scene, sceneManager) {
    console.log('🎯 正在初始化物理系统示例...');

    try {
        // 获取当前场景的物理管理器
        const physicsManager = sceneManager.getCurrentPhysicsManager();
        
        if (!physicsManager) {
            console.warn('当前场景没有物理管理器，无法运行物理示例');
            return null;
        }

        // 等待物理管理器准备就绪
        if (!physicsManager.isReady()) {
            console.log('等待物理管理器初始化...');
            // 简单的等待循环
            let attempts = 0;
            while (!physicsManager.isReady() && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!physicsManager.isReady()) {
                throw new Error('物理管理器初始化超时');
            }
        }

        console.log('✅ 物理管理器已准备就绪');

        // 创建物理示例对象
        const physicsExample = {
            scene: scene,
            physicsManager: physicsManager,
            entities: [],
            
            // 创建地面
            createGround() {
                console.log('创建物理地面...');
                const ground = PhysicsUtils.createPhysicsGround(
                    ecsWorld,
                    physicsManager,
                    scene,
                    {
                        width: 50,
                        height: 50,
                        y: 0,
                        color: Color3.FromHexString('#8B4513') // 棕色
                    }
                );
                
                if (ground) {
                    this.entities.push(ground);
                    console.log('✅ 地面创建成功');
                }
                
                return ground;
            },

            // 创建一些物理球体
            createBalls() {
                console.log('创建物理球体...');
                const balls = [];
                
                for (let i = 0; i < 5; i++) {
                    const position = new Vector3(
                        (Math.random() - 0.5) * 10, // X: -5 到 5
                        5 + i * 2,                   // Y: 5, 7, 9, 11, 13
                        (Math.random() - 0.5) * 10  // Z: -5 到 5
                    );
                    
                    const ball = PhysicsUtils.createPhysicsBall(
                        ecsWorld,
                        physicsManager,
                        scene,
                        position,
                        {
                            color: Color3.FromHSV(i * 72, 1, 1), // 不同颜色
                            name: `Ball_${i}`
                        }
                    );
                    
                    if (ball) {
                        balls.push(ball);
                        this.entities.push(ball);
                    }
                }
                
                console.log(`✅ 创建了 ${balls.length} 个球体`);
                return balls;
            },

            // 创建一些物理箱子
            createBoxes() {
                console.log('创建物理箱子...');
                const boxes = [];
                
                for (let i = 0; i < 3; i++) {
                    const position = new Vector3(
                        i * 3 - 3,  // X: -3, 0, 3
                        10,         // Y: 10
                        5           // Z: 5
                    );
                    
                    const box = PhysicsUtils.createPhysicsBox(
                        ecsWorld,
                        physicsManager,
                        scene,
                        position,
                        {
                            color: Color3.FromHSV(120 + i * 60, 0.8, 0.9),
                            name: `Box_${i}`
                        }
                    );
                    
                    if (box) {
                        boxes.push(box);
                        this.entities.push(box);
                    }
                }
                
                console.log(`✅ 创建了 ${boxes.length} 个箱子`);
                return boxes;
            },

            // 创建触发器区域
            createTriggerZone() {
                console.log('创建触发器区域...');
                
                const triggerCallback = (collisionEvent, triggerBody) => {
                    console.log('🎯 触发器被触发!', {
                        collisionType: collisionEvent.type,
                        otherBody: collisionEvent.collidedAgainst
                    });
                };
                
                const trigger = PhysicsUtils.createPhysicsEntity(
                    ecsWorld,
                    physicsManager,
                    scene,
                    {
                        name: 'TriggerZone',
                        position: new Vector3(0, 2, 0),
                        scale: new Vector3(4, 1, 4),
                        preset: 'TRIGGER',
                        meshType: 'box',
                        materialColor: Color3.FromHexString('#FF6B6B').scale(0.5), // 半透明红色
                        customPhysicsOptions: {
                            isTrigger: true,
                            triggerCallback: triggerCallback
                        }
                    }
                );
                
                if (trigger) {
                    // 设置触发器网格为半透明
                    if (trigger.mesh && trigger.mesh.material) {
                        trigger.mesh.material.alpha = 0.5;
                    }
                    
                    this.entities.push(trigger);
                    console.log('✅ 触发器区域创建成功');
                }
                
                return trigger;
            },

            // 演示射线检测
            demonstrateRaycast() {
                console.log('演示射线检测...');
                
                const origin = new Vector3(0, 15, 0);
                const direction = new Vector3(0, -1, 0); // 向下
                const distance = 20;
                
                const raycastResult = physicsManager.raycast(origin, direction, distance);
                
                if (raycastResult && raycastResult.hasHit) {
                    console.log('🎯 射线检测命中:', {
                        point: raycastResult.point,
                        normal: raycastResult.normal,
                        distance: raycastResult.distance
                    });
                } else {
                    console.log('射线检测未命中任何物体');
                }
                
                return raycastResult;
            },

            // 应用随机力到所有球体
            applyRandomForces() {
                console.log('对球体应用随机力...');
                
                this.entities.forEach(entity => {
                    if (entity.name && entity.name.startsWith('Ball_') && entity.physicsBody) {
                        const randomForce = new Vector3(
                            (Math.random() - 0.5) * 100,
                            Math.random() * 50,
                            (Math.random() - 0.5) * 100
                        );
                        
                        physicsManager.applyForce(entity.physicsBody, randomForce);
                        console.log(`对 ${entity.name} 应用力:`, randomForce);
                    }
                });
            },

            // 获取物理统计信息
            getPhysicsStats() {
                return physicsManager.getStats();
            },

            // 清理示例
            dispose() {
                console.log('清理物理示例...');
                
                this.entities.forEach(entity => {
                    if (entity.physicsBody) {
                        physicsManager.removeRigidBody(entity.physicsBody);
                    }
                    if (entity.mesh) {
                        entity.mesh.dispose();
                    }
                });
                
                this.entities = [];
                console.log('✅ 物理示例清理完成');
            }
        };

        // 创建示例场景
        physicsExample.createGround();
        physicsExample.createBalls();
        physicsExample.createBoxes();
        physicsExample.createTriggerZone();

        // 演示射线检测
        setTimeout(() => {
            physicsExample.demonstrateRaycast();
        }, 1000);

        // 定期应用随机力
        setInterval(() => {
            physicsExample.applyRandomForces();
        }, 5000);

        console.log('✅ 物理系统示例初始化完成');
        console.log('🎮 物理示例控制:');
        console.log('  - window.physicsExample.applyRandomForces() - 应用随机力');
        console.log('  - window.physicsExample.demonstrateRaycast() - 射线检测');
        console.log('  - window.physicsExample.getPhysicsStats() - 获取统计信息');

        return physicsExample;

    } catch (error) {
        console.error('❌ 物理系统示例初始化失败:', error);
        return null;
    }
}

export default { initializePhysicsExample };

console.log('物理系统使用示例已加载');
