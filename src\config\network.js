// src/config/network.js
// network配置文件

/**
 * network配置文件
 * networkManager配置参数和服务器地址参数
 */
export const NetworkConfig = {
    // ws服务器
    serverAddress: "ws://**************:5588/",
    serverPath:    "ws/cantos",
    connId:        "TESTCONNTOKEN00", // 测试使用固定的conn_id

    // 自动重连相关属性
    maxReconnectAttempts:  10,     // 最大重连尝试次数
    initialReconnectDelay: 1000,   // 初始重连延迟(毫秒, 1秒)

    // 心跳机制相关属性
    heartbeatIntervalMs:     10000,   // PING消息发送间隔(25秒), 测试时为1s, 方便看到服务器是否正在运行
    serverActivityTimeoutMs: 30000,   // 服务器活动超时时间(10秒) - 若此时间内未收到任何服务器消息则认为连接丢失, 这个值不能小于heartbeatIntervalMs, 否则会被心跳检测认为是服务器掉线.

    // conspack
    writerBufferSize: 4096, // 初始4M大小的ArrayBuffer, 超出会自动扩容
};
