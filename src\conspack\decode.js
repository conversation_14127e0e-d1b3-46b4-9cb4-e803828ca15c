import * as Types from './types';
import Secure from './secure';
import { PLATFORM_BYTES } from './secure';
import { ENCODE_EMPTY_VECTOR } from './utils';
import Headers from './headers';
import { Reader } from './io';
import Utils from './utils';

/**
 * 解析一个ArrayBuffer, 返回对应的数据结构.
 * @param {ArrayBuffer} bytes
 * @returns {any}
 */
function decode(bytes) {
    const reader = new Reader(bytes);
    const header = reader.dataview_readu8();
    return decode_value(reader, header);
}

/**
 * 根据缓冲区头部字节调用对应的解析函数.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {any}
 */
function decode_value(reader, header = 0xff) {
    // header default to nil in cl-conspack, 0xff is not used and thus it's used as the default value here
    let header_value = header !== 0xff ? header : reader.dataview_readu8();
    let header_type = Headers.decode_header(header_value);
    switch(header_type) {
        case Types.BOOLEAN:     return decode_boolean(header_value);            // √
        case Types.NUMBER:      return decode_number(reader, header_value);     // √
        case Types.STRING:      return decode_string(reader, header_value);     // √
        case Types.CHARACTER:   return decode_character(reader, header_value);  // √
        case Types.SYMBOL:      return decode_symbol(reader, header_value);     // √
        case Types.CONTAINER:   return decode_container(reader, header_value);  // √
        case Types.CONS:        return decode_cons(reader);                     // √
        case Types.PROPERTIES:  return decode_properties(reader);               // √
        case Types.PACKAGE:     return decode_package(reader, header_value);    // going on
        default: throw new Error(`Cannot decode bytes whose heade has value: ${header_value} and type: ${header_type.description}!`);
        //header_type == :package     && return decode_module(buffer)
        //header_type == :tag         && return decode_tag(buffer, header_value)
        //header_type == :ref         && return decode_ref(buffer, header_value)
        //header_type == :r_ref       && return decode_r_ref(buffer, header_value)
        //header_type == :pointer     && return decode_pointer(buffer, header_value)
        //header_type == :index       && return decode_index(buffer, header_value)
    }
}

/**
 * 用于哈希表的解码, 转换成js对象.
 * @param {Reader} reader
 */
function decode_properties(reader) {
    // only hashtable has properties now and the perperties will be ignored for Dict of julia
    decode_value(reader); // decode property of type map such as '(:TEST, :EQUAL) in cl-conspack, but not used in js
    return decode_value(reader);
}

/**
 * 解析布尔类型.
 * @param {uint8} header
 * @returns {boolean}
 */
function decode_boolean(header) {
    Secure.use_bytes(PLATFORM_BYTES);
    if(header == 0x00) { return false; }
    if(header == 0x01) { return true; }
    throw new Error(`Not a boolean header ${header}.`);
}

/**
 * 解析数值类型: 包括整数(最大支持64bit), 单精度浮点数, 双精度浮点数, 有理数
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {number | bigint}
 */
function decode_number(reader, header) {
    let h = Headers.decode_number_header(header);
    switch(h) {
        // integers
        case Types.INT8:    { Secure.use_bytes(1);  return reader.dataview_read8(); }
        case Types.UINT8:   { Secure.use_bytes(1);  return reader.dataview_readu8(); }
        case Types.INT16:   { Secure.use_bytes(2);  return reader.dataview_read16(); }
        case Types.UINT16:  { Secure.use_bytes(2);  return reader.dataview_readu16(); }
        case Types.INT32:   { Secure.use_bytes(4);  return reader.dataview_read32(); }
        case Types.UINT32:  { Secure.use_bytes(4);  return reader.dataview_readu32(); }
        case Types.INT64:   { Secure.use_bytes(8);  return reader.dataview_read64(); }
        case Types.UINT64:  { Secure.use_bytes(8);  return reader.dataview_readu64(); }
        case Types.INT128:  { Secure.use_bytes(16); return reader.read128_be(); }
        case Types.UINT128: { Secure.use_bytes(16); return reader.readu128_be(); }
        // float number
        case Types.SINGLE_FLOAT: { Secure.use_bytes(4); return reader.dataview_read_single(); }
        case Types.DOUBLE_FLOAT: { Secure.use_bytes(8); return reader.dataview_read_double(); }
        // rational number
        case Types.RATIONAL: {
            Secure.use_bytes(PLATFORM_BYTES);
            return rational_check_bigint(decode_value(reader)) / rational_check_bigint(decode_value(reader));
        }
        case Types.COMPLEX: { Secure.use_bytes(PLATFORM_BYTES); return [decode_value(reader), decode_value(reader)]; } // not supported by vanilla js
        default: throw new Error(`Cannot decode this type of number: ${h}.`);
    }

    function rational_check_bigint(maybe_big) {
        if(typeof(maybe_big) === 'number') {
            return maybe_big;
        } else {
            console.warn('BigInt is found in numerator or denominator, this may lose accuracy');
            return Number(maybe_big);
        }
    }
}



/**
 * 解析剩余部分包含多少字节.
 * @param {Reader} reader
 * @param {uint} bytes
 * @returns {int}
 */
function decode_size(reader, bytes) {
    switch(bytes) {
        case 1: return reader.dataview_readu8();
        case 2: return reader.dataview_readu16();
        case 4: return reader.dataview_readu32();
        default: throw new Error(`decode_size failed due to bytes = ${bytes}.`);
    }
}

/**
 * 解析字符串.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {string}
 */
function decode_string(reader, header) {
    let len = decode_size(reader, Headers.size_bytes(header));
    Secure.use_bytes(len);
    let bytes = reader.buffer.slice(reader.buffIndex, reader.buffIndex + len);
    reader.buffIndex += len;
    let text_decoder = reader.textDecoder ? reader.textDecoder : reader.textDecoder = new TextDecoder();
    return text_decoder.decode(new Uint8Array(bytes));
}

/**
 * 其它语言的字符类型解析成js的字符串.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {string}
 */
function decode_character(reader, header) {
    let len = header & 0b11;
    Secure.use_bytes(len);
    let bytes = reader.buffer.slice(reader.buffIndex, reader.buffIndex + len);
    reader.buffIndex += len;
    let text_decoder = reader.textDecoder ? reader.textDecoder : reader.textDecoder = new TextDecoder();
    return text_decoder.decode(new Uint8Array(bytes));
}

/**
 * 解析成符号, 但注意JS版的conspack丢弃了CL符号的包.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {symbol}
 */
function decode_symbol(reader, header) {
    // symbol has two fields: symbol name and module name,
    // if the symbol name can be rerieved from the module, return the object the symbol points to,
    // for other cases (include lisp's keyword type), just make a symbol with symbol name and return this symbol.
    let symbol_name = decode_value(reader);
    let module_string = Headers.keyword_p(header) ? false : decode_value(reader);
    let sym = Symbol.for(symbol_name);
    // module_string === false ? console.log('Get Common Lisp keyword:', sym) : console.log(`Get Common Lisp symbol: ${module_string}:${symbol_name}.`);
    return sym; // return sym whether it's a keyword or a normal symbol
}

/** CL的package解析成字符串 */
function decode_package(reader) {
    return decode_value(reader);
}

/**
 * CL的cons或者Julia的Pair, 解析成js数组.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {any[]}
 */
function decode_cons(reader) {
    // to get compatible with cl-conspack, list type like '(a) is encode as cons
    Secure.use_bytes(PLATFORM_BYTES, 2);
    let first  = decode_value(reader);
    let second = decode_value(reader);
    return second === false ? [first] : [first, second];
}

/**
* Supported container:
* - list, decoded to vector.
* - vector, decoded to vector.
* - map, decoded to Dict
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {any[] | object}
 */
function decode_container(reader, header) {
    let container_type = Headers.decode_container_type(header);
    switch(container_type) {
        case Types.LIST:   return decode_list(reader, header);
        case Types.VECTOR: return decode_vector(reader, header);
        case Types.MAP:    return decode_map(reader, header);
        case Types.TMAP:   return decode_tmap(reader, header); // going on
        default: throw new Error(`Cannot decode this type of container, type = ${container_type}, header = ${header}.`);
    }
}

/**
 * CL的列表解析成JS数组.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {any[]}
 */
function decode_list(reader, header) {
    let lange = decode_size(reader, Headers.size_bytes(header));
    let fixed_header = Headers.container_fixed_p(header) === true ? reader.dataview_readu8() : false;
    Secure.container_precheck_bytes(lange, fixed_header);
    if(lange < 2 && ENCODE_EMPTY_VECTOR === false) {
        throw new Error(`Invalid size, len = ${lange}, reason: Length of LIST must be >= 2!`);
    }
    let result = [];
    for(let i= 0; i < lange - 1; i++) { // 第lange-1个位置是留给CL列表最末尾的位置, 判断是否cons
        result.push(decode_value(reader));
    }
    let last = decode_value(reader);
    if(last !== false) { result.push(last); }
    return result;
}

/**
 * CL的向量解析成JS的数组.
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {any[]}
 */
function decode_vector(reader, header) {
    let lange = decode_size(reader, Headers.size_bytes(header));
    let fixed_header = Headers.container_fixed_p(header) === true ? reader.dataview_readu8() : false;

    Secure.container_precheck_bytes(lange, fixed_header);
    let result = [];
    if(fixed_header) {
        for(let i= 0; i < lange; i++) {
            result.push(decode_value(reader, fixed_header));
        }
    }
    else {
        for(let i= 0; i < lange; i++) {
            result.push(decode_value(reader));
        }
    }

    return result;
}

/**
 * CL的哈希表解析成JS的对象
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {object}
 */
function decode_map(reader, header) {
    let lange = decode_size(reader, Headers.size_bytes(header));
    let fixed_header = Headers.container_fixed_p(header) === true ? reader.dataview_readu8() : false;
    let hash = {};
    Secure.container_precheck_bytes(lange << 1, fixed_header);
    for(let i= 0; i < lange; i++) {
        let key = decode_value(reader);
        let val = decode_value(reader);
        hash[key] = val;
    }
    return hash;
}

/*
(defun encode-tmap (value buffer &optional fixed-header)
  (let* ((encoded-alist (if *ref-context*
                            (gethash value (ref-context-encoded-objects *ref-context*))
                            (encode-object value)))
         (len (length encoded-alist)))
    (format t "buffer: ~d~%" buffer)
    (format t "to encode header: ~d, fixed: ~d, len: ~d~%" (container-header :tmap nil) fixed-header len)
    (encode-header (container-header :tmap nil) buffer fixed-header len)
    (format t "buffer: ~d~%" buffer)
    (format t "to ref-or-value: ~d, symbol package: ~d~%" (object-class-identifier value) (symbol-package (object-class-identifier value)))
    (encode-ref-or-value (object-class-identifier value) buffer)
    (format t "buffer: ~d~%" buffer)
    (format t "encoded-alist: ~d~%" encoded-alist)
    (loop for i in encoded-alist do
          (encode-ref-or-value (car i) buffer)
          (encode-ref-or-value (cdr i) buffer))))
*/

/**
 * CL的一般array(非vector)解析成JS的数组.
 * Note: decode_tmap is not fully tested!
 * @param {Reader} reader
 * @param {uint8} header
 * @returns {object}
 */
function decode_tmap(reader, header) {
    const len = reader.dataview_readu8();
    const tmap_type = decode_value(reader);
    if(tmap_type.description === 'ARRAY') { // tmap is a list of cons
        const dimension    = (decode_value(reader), decode_value(reader));
        const element_type = (decode_value(reader), decode_value(reader));
        const content      = (decode_value(reader), decode_value(reader));

        if(dimension.length < 2) { throw new Error(`Array should be at least 2 dimenstions, but with ${dimension}.`); }

        const matrix = Utils.createArray(...dimension);
        Utils.fillMatrix(matrix, dimension, content);
        return matrix;
    }
    else {
        throw new Error(`This kind of tmap ${tmap_type.description} is not supported!`);
    }

}

export { decode };
