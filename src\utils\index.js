/**
 * 创建一个整数发射器（自增器）。
 * @param {int} initialValue - 发射器开始的初始整数值。
 * @returns {function(): int} - 一个函数，每次调用时返回下一个整数。
 * 示例用法：
 * 创建一个从 1 开始的发射器
 * const emitter1 = createIntegerEmitter(1);
 * console.log(emitter1()); // 输出: 1
 * console.log(emitter1()); // 输出: 2
 * console.log(emitter1()); // 输出: 3
 *
 * 创建另一个从 100 开始的独立发射器
 * const emitter2 = createIntegerEmitter(100);
 * console.log(emitter2()); // 输出: 100
 * console.log(emitter2()); // 输出: 101
 * console.log(emitter1()); // 仍然输出: 4 (emitter1 和 emitter2 是独立的)
 */
export const createIntegerEmitter = (initialValue) => {
  let currentValue = initialValue;
  return function() {
    return currentValue++;
  };
};

/**
 * Returns the first argument it receives.
 * @param {*} value Any value.
 * @returns {*} Returns `value`.
 * @example
 * const object = { 'a': 1 };
 * console.log(identity(object) === object); // => true
 */
export const identity = value => value;


/**
 * When `value` is true, return the result of calling `trueCall` with `value`, or return the result of calling `falseCall`.
 * @param {any} value
 * @param {(value) => {}} trueCall Call if value is true and return the result
 * @param {(value)=>{}} falseCall Call if value is false and return the result
 */
export const ifLet = (value, trueCall = identity, falseCall = identity) => value ? trueCall(value) : falseCall(value);


/**
 * When `truthTest(value)` is true, return the result of calling `trueCall` with `value`, or return the result of calling `falseCall`.
 * @param {any} value
 * * @param {(value) => {}} truthTest Test if value is true in the context
 * @param {(value) => {}} trueCall Call if value is true and return the result
 * @param {(value)=>{}} falseCall Call if value is false and return the result
 */
export const ifTruthLet = (value, truthTest = identity, trueCall = identity, falseCall = identity) => truthTest(value) ? trueCall(value) : falseCall(value);


/**
 * When `value` is true, return the result of calling `trueCall` with `value`, or return `defaultRet`.
 * @param {any} value
 * @param {(value) => {}} trueCall Call if value is true and return the result.
 * @param {any} defaultRet Return if `value` is false.
 */
export const whenLet = (value, trueCall = identity, defaultRet = value) => value ? trueCall(value) : defaultRet;
