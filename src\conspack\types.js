//// Each type is defined as a symbol
//// Some types are not supported such by js,
//// however, they may be extended with Math.js.


const INT8         = Symbol("int8");
const INT16        = Symbol("int16");
const INT32        = Symbol("int32");
const INT64        = Symbol("int64");
const INT128       = Symbol("int128");
const UINT8        = Symbol("uint8");
const UINT16       = Symbol("uint16");
const UINT32       = Symbol("uint32");
const UINT64       = Symbol("uint64");
const UINT128      = Symbol("uint128");
const SINGLE_FLOAT = Symbol("single_float");
const DOUBLE_FLOAT = Symbol("double_float");
const COMPLEX      = Symbol("complex");
const RATIONAL     = Symbol("rational");

const BOOLEAN      = Symbol("boolean");
const NUMBER       = Symbol("number");
const STRING       = Symbol("string");
const CONTAINER    = Symbol("container");
const CHARACTER    = Symbol("character");
const PACKAGE      = Symbol("package");
const KEYWORD       = Symbol("keyword");
const SYMBOL       = Symbol("symbol");
const CONS         = Symbol("cons");
const PROPERTIES   = Symbol("properties");
const INDEX        = Symbol("index");
const REF          = Symbol("ref");
const R_REF        = Symbol("r-ref");
const POINTER      = Symbol("pointer");
const TAG          = Symbol("tag");

const VECTOR = Symbol("vector");
const LIST   = Symbol("list");
const MAP    = Symbol("map");
const TMAP   = Symbol("tmap");

const NUMBER_SYMBOLS_SET = new Set([INT8, INT16, INT32, INT64, INT128,
                              UINT8, UINT16, UINT32, UINT64, UINT128,
                              SINGLE_FLOAT, DOUBLE_FLOAT,
                              COMPLEX, RATIONAL]);

const CONTAINER_SYMBOLS_SET = new Set([VECTOR, LIST, MAP, TMAP]);


export { BOOLEAN, NUMBER, STRING, CONTAINER,
         CHARACTER, PACKAGE, KEYWORD, SYMBOL, CONS, PROPERTIES,
         INDEX, REF, R_REF, POINTER, TAG };

export { INT8, INT16, INT32, INT64, INT128,
         UINT8, UINT16, UINT32, UINT64, UINT128,
         SINGLE_FLOAT, DOUBLE_FLOAT,
         COMPLEX, RATIONAL };

export { VECTOR, LIST, MAP, TMAP };

export { NUMBER_SYMBOLS_SET, CONTAINER_SYMBOLS_SET };
