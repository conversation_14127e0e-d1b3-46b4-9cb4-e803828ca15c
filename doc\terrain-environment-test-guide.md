# 地形和环境系统测试指南

## 测试概述

本文档描述了如何测试新实现的地形生成和环境系统功能。

## 已实现的功能

### ✅ 地形生成系统
- **程序化地形生成**: 使用柏林噪声算法生成自然地形
- **多层材质系统**: 基于高度的材质混合（草地、岩石、雪地）
- **物理碰撞**: 地形具有完整的物理碰撞体
- **配置化**: 支持多种地形预设和自定义配置

### ✅ 环境系统
- **昼夜循环**: 动态的太阳位置和光照变化
- **天气系统**: 支持多种天气类型（晴天、多云、雨天等）
- **动态光照**: 太阳光、环境光、月光的自动切换
- **雾效系统**: 基于时间和天气的动态雾效
- **天空盒**: 程序化天空渲染

### ✅ 山海经主题
- **五大区域配置**: 东山经、南山经、西山经、北山经、中山经
- **文化元素**: 古代时辰系统、五行元素效果
- **神秘效果**: 仙气、灵光、神兽光环等特效配置

## 测试步骤

### 1. 基础功能测试

#### 启动游戏
1. 打开浏览器访问 `http://localhost:8080`
2. 等待场景加载完成
3. 观察控制台输出，确认没有错误

#### 地形测试
1. **视觉检查**:
   - 地形应该显示为起伏的山丘
   - 具有自然的高低变化
   - 材质应该根据高度有所不同

2. **物理测试**:
   - 使用WASD移动玩家
   - 玩家应该能在地形上正常行走
   - 空格跳跃功能正常
   - 玩家不会穿透地形

3. **相机控制**:
   - 鼠标拖拽旋转视角
   - 滚轮缩放距离
   - 相机跟随玩家移动

### 2. 环境系统测试

#### 昼夜循环测试
1. **观察光照变化**:
   - 场景光照应该随时间变化
   - 太阳位置会移动
   - 颜色从黎明→白天→黄昏→夜晚循环

2. **控制台测试**:
   ```javascript
   // 在浏览器控制台中执行
   // 设置为正午
   window.sceneModule.environmentManager.setTimeOfDay(0.5);
   
   // 设置为午夜
   window.sceneModule.environmentManager.setTimeOfDay(0.0);
   
   // 设置为黄昏
   window.sceneModule.environmentManager.setTimeOfDay(0.75);
   ```

#### 天气系统测试
1. **切换天气**:
   ```javascript
   // 在浏览器控制台中执行
   // 设置为晴天
   window.sceneModule.setWeather('clear');
   
   // 设置为多云
   window.sceneModule.setWeather('cloudy');
   
   // 设置为雨天
   window.sceneModule.setWeather('lightRain');
   
   // 设置为雾天
   window.sceneModule.setWeather('fog');
   ```

2. **观察变化**:
   - 光照强度变化
   - 雾效密度变化
   - 整体氛围变化

### 3. 性能测试

#### FPS监控
1. **查看性能**:
   ```javascript
   // 在浏览器控制台中执行
   console.log(window.sceneModule.getSceneInfo());
   ```

2. **性能指标**:
   - FPS应该保持在30以上
   - 内存使用合理
   - 无明显卡顿

#### 压力测试
1. **快速切换**:
   - 快速切换天气
   - 快速改变时间
   - 观察是否有性能问题

### 4. 调试功能测试

#### 调试对象访问
```javascript
// 在浏览器控制台中可以访问的调试对象
window.scene              // Babylon.js场景
window.sceneModule        // 山海经世界场景模块
window.terrainGenerator   // 地形生成器
window.environmentManager // 环境管理器
window.localPlayer        // 本地玩家
```

#### 地形重新生成
```javascript
// 生成新的随机地形
window.terrainGenerator.generateTerrain('mountains', Math.random() * 100000);
```

#### 环境信息查看
```javascript
// 查看当前环境状态
const timeInfo = window.environmentManager.getTimeInfo();
console.log('当前时间信息:', timeInfo);
```

## 预期结果

### 正常运行标准
1. **无错误启动**: 控制台无红色错误信息
2. **地形正确显示**: 可以看到起伏的地形
3. **玩家正常移动**: WASD移动，空格跳跃
4. **光照动态变化**: 可以观察到昼夜循环
5. **性能稳定**: FPS保持在合理范围

### 成功标志
- 控制台显示: "🏔️ 山海经世界已成功启动！"
- 地形生成完成提示
- 环境系统初始化完成提示
- 玩家可以在3D世界中自由移动

## 常见问题排查

### 地形不显示
1. 检查控制台是否有地形生成错误
2. 确认Havok物理引擎加载成功
3. 检查WebGL支持情况

### 性能问题
1. 降低地形细分数 (subdivisions)
2. 减少纹理分辨率
3. 关闭不必要的特效

### 光照异常
1. 检查环境管理器初始化
2. 确认光照系统正常创建
3. 验证时间设置是否正确

## 下一步开发

基于测试结果，下一步可以考虑：
1. 优化地形生成算法
2. 添加更多天气效果
3. 实现季节变化
4. 添加植被系统
5. 实现水体效果

## 反馈和改进

测试过程中发现的问题请记录：
- 性能瓶颈
- 视觉效果问题
- 用户体验问题
- 功能缺陷

这些反馈将用于后续的优化和改进。
