/**
 * bitECS使用示例
 * 展示如何在Cantos MMORPG项目中使用bitECS库
 */

import { ecsWorld, Transform, Physics, PlayerInput, Health } from '../core/ecs/bitECSSystem.js';
import { movementSystem, createPlayerEntity, setPlayerInput } from '../core/ecs/systems/MovementSystem.js';

/**
 * 初始化ECS示例
 * 创建一些示例实体和系统
 */
export function initializeBitECSExample() {
    console.log('🎮 开始bitECS使用示例...');

    // 注册移动系统
    ecsWorld.registerSystem(movementSystem, 'MovementSystem');

    // 创建玩家实体
    const player = createPlayerEntity(ecsWorld, 0, 0, 0);

    // 为玩家添加生命值组件
    ecsWorld.addComponent(player, Health, {
        current: 100,
        maximum: 100,
        regeneration: 1.0
    });

    // 创建一些NPC实体
    const npc1 = createNPCEntity(ecsWorld, 10, 0, 10);
    const npc2 = createNPCEntity(ecsWorld, -10, 0, -10);

    console.log(`✅ 创建了 ${3} 个实体`);

    // 启动ECS系统
    ecsWorld.start();

    // 设置游戏循环
    let lastTime = performance.now();

    function gameLoop() {
        const currentTime = performance.now();
        const deltaTime = (currentTime - lastTime) / 1000; // 转换为秒
        lastTime = currentTime;

        // 更新ECS系统
        ecsWorld.update(deltaTime);

        // 继续游戏循环
        requestAnimationFrame(gameLoop);
    }

    // 开始游戏循环
    gameLoop();

    // 设置键盘输入监听
    setupInputHandling(player);

    // 暴露到全局作用域以便调试
    window.playerEntity = player;
    window.npcEntities = [npc1, npc2];

    console.log('🎉 bitECS示例初始化完成！');
    console.log('💡 使用WASD键移动玩家，空格键跳跃');
    console.log('🔧 可以通过window.playerEntity访问玩家实体');

    return { player, npcs: [npc1, npc2] };
}

/**
 * 创建NPC实体
 * @param {Object} ecsWorld - ECS世界实例
 * @param {number} x - X坐标
 * @param {number} y - Y坐标
 * @param {number} z - Z坐标
 * @returns {number} NPC实体ID
 */
function createNPCEntity(ecsWorld, x = 0, y = 0, z = 0) {
    const entity = ecsWorld.createEntity();

    // 添加Transform组件
    ecsWorld.addComponent(entity, Transform, {
        x: x,
        y: y,
        z: z,
        rotationX: 0,
        rotationY: 0,
        rotationZ: 0,
        scaleX: 1,
        scaleY: 1,
        scaleZ: 1
    });

    // 添加Physics组件（静态NPC）
    ecsWorld.addComponent(entity, Physics, {
        mass: 60.0,
        velocityX: 0,
        velocityY: 0,
        velocityZ: 0,
        friction: 0.9,
        restitution: 0.1,
        isKinematic: 0,
        isStatic: 1  // 静态NPC
    });

    // 添加生命值组件
    ecsWorld.addComponent(entity, Health, {
        current: 80,
        maximum: 80,
        regeneration: 0.5
    });

    console.log(`创建NPC实体: ${entity} 位置: (${x}, ${y}, ${z})`);
    return entity;
}

/**
 * 设置输入处理
 * @param {number} playerEntity - 玩家实体ID
 */
function setupInputHandling(playerEntity) {
    const inputState = {
        moveForward: false,
        moveBackward: false,
        moveLeft: false,
        moveRight: false,
        jump: false,
        mouseX: 0,
        mouseY: 0
    };

    // 键盘按下事件
    document.addEventListener('keydown', (event) => {
        switch(event.code) {
            case 'KeyW':
                inputState.moveForward = true;
                break;
            case 'KeyS':
                inputState.moveBackward = true;
                break;
            case 'KeyA':
                inputState.moveLeft = true;
                break;
            case 'KeyD':
                inputState.moveRight = true;
                break;
            case 'Space':
                inputState.jump = true;
                event.preventDefault();
                break;
        }

        // 更新玩家输入组件
        setPlayerInput(playerEntity, inputState);
    });

    // 键盘释放事件
    document.addEventListener('keyup', (event) => {
        switch(event.code) {
            case 'KeyW':
                inputState.moveForward = false;
                break;
            case 'KeyS':
                inputState.moveBackward = false;
                break;
            case 'KeyA':
                inputState.moveLeft = false;
                break;
            case 'KeyD':
                inputState.moveRight = false;
                break;
            case 'Space':
                inputState.jump = false;
                break;
        }

        // 更新玩家输入组件
        setPlayerInput(playerEntity, inputState);
    });

    console.log('⌨️ 输入处理已设置');
}

/**
 * 生命值恢复系统
 * 处理所有具有Health组件的实体的生命值恢复
 * @param {Object} world - ECS世界实例
 * @param {number} deltaTime - 时间增量
 */
export function healthRegenerationSystem(world, deltaTime) {
    const entities = ecsWorld.query([Health]);

    for (const entity of entities) {

        const current = Health.current[entity];
        const maximum = Health.maximum[entity];
        const regeneration = Health.regeneration[entity];

        // 如果生命值未满，则恢复生命值
        if (current < maximum) {
            const newHealth = Math.min(maximum, current + regeneration * deltaTime);
            Health.current[entity] = newHealth;

            // 输出恢复信息（仅用于调试）
            if (Math.floor(newHealth) !== Math.floor(current)) {
                console.log(`实体 ${entity} 生命值恢复到: ${Math.floor(newHealth)}/${maximum}`);
            }
        }
    }
}

/**
 * 位置日志系统
 * 定期输出实体位置信息（用于调试）
 * @param {Object} world - ECS世界实例
 * @param {number} deltaTime - 时间增量
 */
export function positionLogSystem(world, deltaTime) {
    // 每5秒输出一次位置信息
    positionLogSystem.timer = (positionLogSystem.timer || 0) + deltaTime;

    if (positionLogSystem.timer >= 5.0) {
        positionLogSystem.timer = 0;

        const entities = ecsWorld.query([Transform]);
        console.log('📍 实体位置信息:');

        for (const entity of entities) {
            const x = Transform.x[entity].toFixed(2);
            const y = Transform.y[entity].toFixed(2);
            const z = Transform.z[entity].toFixed(2);

            console.log(`  实体 ${entity}: (${x}, ${y}, ${z})`);
        }
    }
}

// 如果直接运行此文件，初始化示例
if (typeof window !== 'undefined') {
    window.initializeBitECSExample = initializeBitECSExample;
    window.healthRegenerationSystem = healthRegenerationSystem;
    window.positionLogSystem = positionLogSystem;
}
