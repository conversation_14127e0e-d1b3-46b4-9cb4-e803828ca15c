// src/core/assets/index.js
// 资源管理系统主入口文件

import AssetManager, { AssetType, AssetStatus } from './AssetManager.js';
import AssetLoaders from './AssetLoaders.js';
import AssetCache from './AssetCache.js';
import LoadingProgressManager, { ProgressStatus } from './LoadingProgressManager.js';

/**
 * 创建资源管理器实例
 * @param {Object} options - 配置选项
 * @returns {AssetManager} 资源管理器实例
 */
export function createAssetManager(options = {}) {
    return new AssetManager(options);
}

/**
 * 全局资源管理器实例
 * 提供便捷的全局访问方式
 */
let globalAssetManager = null;

/**
 * 初始化全局资源管理器
 * @param {Object} options - 配置选项
 * @returns {AssetManager} 全局资源管理器实例
 */
export function initializeGlobalAssetManager(options = {}) {
    if (globalAssetManager) {
        console.warn('全局资源管理器已存在，将替换现有实例');
        globalAssetManager.dispose();
    }

    globalAssetManager = new AssetManager(options);
    
    // 暴露到全局作用域（开发调试用）
    if (typeof window !== 'undefined') {
        window.assetManager = globalAssetManager;
    }

    console.log('全局资源管理器已初始化');
    return globalAssetManager;
}

/**
 * 获取全局资源管理器实例
 * @returns {AssetManager|null} 全局资源管理器实例
 */
export function getGlobalAssetManager() {
    return globalAssetManager;
}

/**
 * 资源预设配置
 */
export const AssetPresets = {
    // 低内存配置
    LOW_MEMORY: {
        maxCacheSize: 50 * 1024 * 1024, // 50MB
        enableAutoGC: true,
        gcThreshold: 0.7,
        retryAttempts: 2
    },

    // 标准配置
    STANDARD: {
        maxCacheSize: 100 * 1024 * 1024, // 100MB
        enableAutoGC: true,
        gcThreshold: 0.8,
        retryAttempts: 3
    },

    // 高性能配置
    HIGH_PERFORMANCE: {
        maxCacheSize: 200 * 1024 * 1024, // 200MB
        enableAutoGC: true,
        gcThreshold: 0.9,
        retryAttempts: 3,
        enablePreloading: true
    },

    // 开发模式配置
    DEVELOPMENT: {
        maxCacheSize: 50 * 1024 * 1024, // 50MB
        enableAutoGC: false, // 开发时禁用自动GC便于调试
        gcThreshold: 0.95,
        retryAttempts: 1
    }
};

/**
 * 常用资源类型检测
 */
export const AssetTypeDetector = {
    /**
     * 根据URL检测资源类型
     * @param {string} url - 资源URL
     * @returns {string} 资源类型
     */
    detectType(url) {
        const extension = url.split('.').pop().toLowerCase();
        
        if (['glb', 'gltf', 'babylon', 'obj', 'fbx'].includes(extension)) {
            return AssetType.MODEL;
        } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'dds', 'ktx', 'webp'].includes(extension)) {
            return AssetType.TEXTURE;
        } else if (['mp3', 'wav', 'ogg', 'aac', 'm4a'].includes(extension)) {
            return AssetType.SOUND;
        } else if (['json', 'xml', 'yaml', 'yml', 'toml'].includes(extension)) {
            return AssetType.CONFIG;
        }
        
        return AssetType.UNKNOWN;
    },

    /**
     * 检查是否为支持的模型格式
     * @param {string} url - 资源URL
     * @returns {boolean} 是否为模型格式
     */
    isModel(url) {
        return this.detectType(url) === AssetType.MODEL;
    },

    /**
     * 检查是否为支持的纹理格式
     * @param {string} url - 资源URL
     * @returns {boolean} 是否为纹理格式
     */
    isTexture(url) {
        return this.detectType(url) === AssetType.TEXTURE;
    },

    /**
     * 检查是否为支持的音频格式
     * @param {string} url - 资源URL
     * @returns {boolean} 是否为音频格式
     */
    isSound(url) {
        return this.detectType(url) === AssetType.SOUND;
    },

    /**
     * 检查是否为支持的配置格式
     * @param {string} url - 资源URL
     * @returns {boolean} 是否为配置格式
     */
    isConfig(url) {
        return this.detectType(url) === AssetType.CONFIG;
    }
};

/**
 * 资源管理工具函数
 */
export const AssetUtils = {
    /**
     * 批量创建资源描述符
     * @param {Array<string>} urls - URL数组
     * @param {Object} commonOptions - 通用选项
     * @returns {Array<Object>} 资源描述符数组
     */
    createAssetDescriptors(urls, commonOptions = {}) {
        return urls.map(url => ({
            url,
            type: AssetTypeDetector.detectType(url),
            options: { ...commonOptions }
        }));
    },

    /**
     * 按类型分组资源
     * @param {Array<Object>} assets - 资源数组
     * @returns {Object} 按类型分组的资源
     */
    groupAssetsByType(assets) {
        const groups = {
            [AssetType.MODEL]: [],
            [AssetType.TEXTURE]: [],
            [AssetType.SOUND]: [],
            [AssetType.CONFIG]: [],
            [AssetType.UNKNOWN]: []
        };

        assets.forEach(asset => {
            const type = asset.type || AssetTypeDetector.detectType(asset.url);
            if (groups[type]) {
                groups[type].push(asset);
            } else {
                groups[AssetType.UNKNOWN].push(asset);
            }
        });

        return groups;
    },

    /**
     * 计算资源总大小估算
     * @param {Array<Object>} assets - 资源数组
     * @returns {number} 估算的总大小（字节）
     */
    estimateTotalSize(assets) {
        // 这是一个简化的大小估算
        const sizeEstimates = {
            [AssetType.MODEL]: 2 * 1024 * 1024, // 2MB
            [AssetType.TEXTURE]: 512 * 1024,    // 512KB
            [AssetType.SOUND]: 1 * 1024 * 1024, // 1MB
            [AssetType.CONFIG]: 10 * 1024,      // 10KB
            [AssetType.UNKNOWN]: 100 * 1024     // 100KB
        };

        return assets.reduce((total, asset) => {
            const type = asset.type || AssetTypeDetector.detectType(asset.url);
            return total + (sizeEstimates[type] || sizeEstimates[AssetType.UNKNOWN]);
        }, 0);
    },

    /**
     * 验证资源URL
     * @param {string} url - 资源URL
     * @returns {boolean} URL是否有效
     */
    validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            // 检查相对路径
            return typeof url === 'string' && url.length > 0 && !url.includes('..') && !url.startsWith('/');
        }
    },

    /**
     * 标准化资源URL
     * @param {string} url - 原始URL
     * @param {string} baseUrl - 基础URL
     * @returns {string} 标准化后的URL
     */
    normalizeUrl(url, baseUrl = '') {
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:')) {
            return url;
        }

        if (baseUrl && !baseUrl.endsWith('/')) {
            baseUrl += '/';
        }

        return baseUrl + url.replace(/^\/+/, '');
    }
};

/**
 * 便捷的资源加载函数
 */
export const AssetHelpers = {
    /**
     * 快速加载模型
     * @param {string} url - 模型URL
     * @param {Object} scene - Babylon.js场景
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 模型资源
     */
    async loadModel(url, scene, options = {}) {
        const manager = getGlobalAssetManager();
        if (!manager) {
            throw new Error('全局资源管理器未初始化，请先调用 initializeGlobalAssetManager()');
        }
        return manager.loadModel(url, { scene, ...options });
    },

    /**
     * 快速加载纹理
     * @param {string} url - 纹理URL
     * @param {Object} scene - Babylon.js场景
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 纹理资源
     */
    async loadTexture(url, scene, options = {}) {
        const manager = getGlobalAssetManager();
        if (!manager) {
            throw new Error('全局资源管理器未初始化，请先调用 initializeGlobalAssetManager()');
        }
        return manager.loadTexture(url, { scene, ...options });
    },

    /**
     * 快速加载音频
     * @param {string} url - 音频URL
     * @param {Object} scene - Babylon.js场景
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 音频资源
     */
    async loadSound(url, scene, options = {}) {
        const manager = getGlobalAssetManager();
        if (!manager) {
            throw new Error('全局资源管理器未初始化，请先调用 initializeGlobalAssetManager()');
        }
        return manager.loadSound(url, { scene, ...options });
    },

    /**
     * 快速加载配置
     * @param {string} url - 配置URL
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 配置数据
     */
    async loadConfig(url, options = {}) {
        const manager = getGlobalAssetManager();
        if (!manager) {
            throw new Error('全局资源管理器未初始化，请先调用 initializeGlobalAssetManager()');
        }
        return manager.loadConfig(url, options);
    },

    /**
     * 批量预加载资源
     * @param {Array<Object>} assetList - 资源列表
     * @param {Object} options - 选项
     * @returns {Promise<Object>} 预加载结果
     */
    async preloadAssets(assetList, options = {}) {
        const manager = getGlobalAssetManager();
        if (!manager) {
            throw new Error('全局资源管理器未初始化，请先调用 initializeGlobalAssetManager()');
        }
        return manager.preloadAssets(assetList, options);
    }
};

// 导出所有核心类和枚举
export {
    AssetManager,
    AssetLoaders,
    AssetCache,
    LoadingProgressManager,
    AssetType,
    AssetStatus,
    ProgressStatus
};

// 默认导出
export default {
    AssetManager,
    AssetLoaders,
    AssetCache,
    LoadingProgressManager,
    AssetType,
    AssetStatus,
    ProgressStatus,
    AssetPresets,
    AssetTypeDetector,
    AssetUtils,
    AssetHelpers,
    createAssetManager,
    initializeGlobalAssetManager,
    getGlobalAssetManager
};
