# conspack.js

`conspack.js` is a [cl-conspack](https://github.com/conspack/cl-conspack) implementation in `Javascript`, and it only implemented a subset functions of `cl-conspack`. The purpose of this package it to exchange data between `Common Lisp` programs via computer networks.

`conspack.js exports `encode` and `decode` and their usages are as same as their `Common Lisp` counterparts.

- `encode` accepts a `Javascript` value and serializes it to an `ArrayBuffer`.

- `decode` accepts a `Javascript` `ArrayBuffUsageer` and deserializes it to an `Javascript` value.

### Usage
```js
> encode([1, 2, 3]);
```
Will get the result as:
```
ArrayBuffer {
  [Uint8Contents]: <28 04 10 01 10 02 10 03 00>,
  byteLength: 9
}
```

```Julia
> decode(encode([1, 2, 3]))
```
Will restore the byte vector to a vector of `[1, 2, 2]`.

`Writer` is a class which is exported and its instance can be passed as the first argument of `encode`, in this case, the underlaid `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` will be reused for all the encodings. This is **recommended** if one has successive data to encode. The underlaid `<PERSON><PERSON>y<PERSON>uff<PERSON>` will not be overflowed.

```js
> const writer = new Writer();
> const val1 = encode(writer, [1, 2, 3]); // encode as default LIST header
> const val2 = encode(writer, {"A": 1, "B": 2});
```

By default, `conspack.js` encode an array as *list* header, it can also be encoded as *vector* header, this should be careful when an compound data structure is about to be encoded, since the change is global and *list* is used in some cases to save some internal information.

```js
> const writer = new Writer();
> changeArrayEncodeType(VECTOR);          // VECTOR is a symbol which should be imported first
> const val1 = encode(writer, [1, 2, 3]); // encode as VECTOR header
> resetArrayEncodeType();                 // restore to LIST header
```

There's also an Julia version of [`conspack.jl`](https://gitee.com/hxz/conspack.jl).

### Supported Types



|    Common Lisp    |           Julia           |                 Javascript                  |
| :---------------: | :-----------------------: | :-----------------------------------------: |
|     `boolean`     |          `Bool`           |                  `Boolean`                  |
|        N/A        |            N/A            |          `null`, decoded to false           |
|        N/A        |            N/A            |        `undefined`, decoded to false        |
| integer types[^1] | `Integer`, up to 128 bits |     `Number`or`BigInt`, up to 128 bits      |
|  `single-float`   |         `Float32`         |            decoded to `Float64`             |
|  `double-float`   |         `Float64`         |                  `Float64`                  |
|     `complex`     |         `Complex`         |          decode to `[real, imag]`           |
|    `rational`     |        `Rational`         |           decode to `Number`[^9]            |
|     `string`      |         `String`          |                  `String`                   |
|    `character`    |          `Char`           |             decoded to `String`             |
|     `package`     |         `Module`          |                   String                    |
|     `keyword`     |         `Symbol`          |                  `Symbol`                   |
|     `symbol`      |        `Function`         |                `Symbol`[^7]                 |
|      `cons`       |        `Pair`[^2]         |                    List                     |
|    `list`[^3]     |         `Vector`          |                   `Array`                   |
|   `vector`[^4]    |          `Tuple`          |                   `Array`                   |
|     `map`[^5]     |          `Dict`           |                  `Object`                   |
|    `tmap`[^6]     |     Not supported yet     | decode to `Array`, partially supported[^10] |
|     `vector`      |          `Array`          |                   `Array`                   |
|     CLOS[^8]      |            N/A            |                     N/A                     |

[^1]: integer subtypes: `int8` .. `int128`, `uint8` .. `uint128`. For `conspack.js`, the integers between `Number.MIN_SAFE_INTEGER` and `Number.MAX_SAFE_INTEGER` will be decoded as `Number.Integer` type, and 8 or 16 byte integers not in that range will be decoded as `BigInt`.
[^2]: `cl-conspack` encodes a singleton list such as `(1)` with `cons` header, `conspack.js` encodes a singleton vector such as `[1]` with `list` header, however, both of the encoded bytes can be correctly decoded by each other.
[^3]: `Javascript` has no list type, and thus JS `Array` --> Lisp `List` by default, and Lisp `List -->  `Array`.
[^4]: Julia `Tuple` --> Lisp `vector` --> Julia `Vector` --> JS `Array`.
[^5]: `hash-table` in common lisp.
[^6]: `cl-conspack` encode general arrays with `tmap`. `(explain  (encode (make-array '(2 3) :element-type '(unsigned-byte 8) :initial-element 0)))`. Note that general arrays' encoding will get rather long results.
[^7]: For a CL symbol like `PACKAGE:SYMBOL`, `conspack,js` will discard the package part and make a symbol with later part.
[^8]: DO NOT use CLOS to share data between different languages, it's very large! 
[^9 ]: When the decoded numerator or denominator is a BigInt, `conspack.js` will convert it to Number first and then make a division, this may bring some accuracy loss.

[^10 ]:`conspack.js` only supports `tmaps` encoded from general arrays, aka two or higher order matrices. 

