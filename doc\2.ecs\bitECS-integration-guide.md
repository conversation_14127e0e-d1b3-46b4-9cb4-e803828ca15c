# bitECS集成指南

本文档说明如何在Cantos MMORPG项目中使用bitECS库。

## 概述

bitECS是一个高性能的Entity-Component-System (ECS) 库，专为JavaScript/TypeScript设计。我们已经将bitECS 0.4.0版本集成到项目中，用于实现游戏的核心架构。

bitECS 0.4.0采用了更灵活的设计理念：
- 实体是数字ID
- 组件存储可以是任何形式
- 没有正式的系统概念，只有查询
- 推荐使用SoA (Structure of Arrays) 格式以获得最佳性能

## 项目配置

### 1. 库位置
- bitECS库位于 `libs/bitECS/` 目录
- 使用构建后的ES模块文件：`libs/bitECS/dist/core/index.min.mjs`

### 2. Webpack配置
在 `webpack.common.cjs` 中已配置别名：
```javascript
alias: {
    'bitecs': path.resolve(appDirectory, 'libs/bitECS/dist/core/index.min.mjs'),
    'bitecs/serialization': path.resolve(appDirectory, 'libs/bitECS/dist/serialization/index.min.mjs'),
    'bitecs/legacy': path.resolve(appDirectory, 'libs/bitECS/dist/legacy/index.min.mjs')
}
```

### 3. Package.json配置
已添加本地依赖：
```json
"bitecs": "file:./libs/bitECS"
```

## 使用方法

### 基本导入
```javascript
import {
    createWorld,
    addEntity,
    addComponent,
    removeComponent,
    hasComponent,
    query
} from 'bitecs';
```

### 预定义组件

项目中已预定义了常用组件（位于 `src/core/ecs/bitECSSystem.js`），使用SoA格式：

#### Transform组件 - 变换信息
```javascript
const Transform = {
    x: [],              // X坐标
    y: [],              // Y坐标
    z: [],              // Z坐标
    rotationX: [],      // X轴旋转
    rotationY: [],      // Y轴旋转
    rotationZ: [],      // Z轴旋转
    scaleX: [],         // X轴缩放
    scaleY: [],         // Y轴缩放
    scaleZ: []          // Z轴缩放
};
```

#### Physics组件 - 物理属性
```javascript
const Physics = {
    mass: [],           // 质量
    velocityX: [],      // X轴速度
    velocityY: [],      // Y轴速度
    velocityZ: [],      // Z轴速度
    friction: [],       // 摩擦力
    restitution: [],    // 弹性系数
    isKinematic: [],    // 是否为运动学物体
    isStatic: []        // 是否为静态物体
};
```

#### 其他组件
- `Render` - 渲染相关
- `Animation` - 动画控制
- `Network` - 网络同步
- `Health` - 生命值系统
- `PlayerInput` - 玩家输入

### ECS世界管理器

使用 `BitECSWorld` 类管理ECS系统：

```javascript
import { ecsWorld } from './core/ecs/bitECSSystem.js';

// 创建实体
const entity = ecsWorld.createEntity();

// 添加组件
ecsWorld.addComponent(entity, Transform, {
    x: 10, y: 0, z: 5,
    rotationX: 0, rotationY: 0, rotationZ: 0,
    scaleX: 1, scaleY: 1, scaleZ: 1
});

// 注册系统
ecsWorld.registerSystem(movementSystem, 'MovementSystem');

// 启动ECS系统
ecsWorld.start();
```

### 创建系统

系统是处理具有特定组件的实体的函数：

```javascript
import { query } from 'bitecs';
import { Transform, Physics } from '../bitECSSystem.js';

export function movementSystem(world, deltaTime) {
    const entities = query(world, [Transform, Physics]);

    for (const entity of entities) {
        // 更新位置
        Transform.x[entity] += Physics.velocityX[entity] * deltaTime;
        Transform.y[entity] += Physics.velocityY[entity] * deltaTime;
        Transform.z[entity] += Physics.velocityZ[entity] * deltaTime;
    }
}
```

## 示例：创建玩家实体

```javascript
import { ecsWorld, Transform, Physics, PlayerInput } from './core/ecs/bitECSSystem.js';
import { createPlayerEntity, setPlayerInput } from './core/ecs/systems/MovementSystem.js';

// 创建玩家实体
const playerEntity = createPlayerEntity(ecsWorld, 0, 0, 0);

// 设置玩家输入
setPlayerInput(playerEntity, {
    moveForward: true,
    moveBackward: false,
    moveLeft: false,
    moveRight: false,
    jump: false
});
```

## 调试和测试

### 测试bitECS功能
运行项目时会自动执行bitECS测试，检查库是否正常工作。

### 全局调试对象
以下对象已暴露到全局作用域：
- `window.ecsWorld` - ECS世界实例
- `window.BitECSComponents` - 所有预定义组件

### 控制台测试
```javascript
// 在浏览器控制台中测试
const entity = window.ecsWorld.createEntity();
window.ecsWorld.addComponent(entity, window.BitECSComponents.Transform, {x: 10, y: 0, z: 5});
```

## 性能优化建议

1. **使用查询缓存**：避免在每帧重新创建查询
2. **批量操作**：尽可能批量处理实体
3. **组件设计**：保持组件数据结构简单
4. **系统顺序**：合理安排系统执行顺序

## 常见问题

### Q: 如何添加新组件？
A: 在 `bitECSSystem.js` 中使用 `defineComponent` 定义新组件，并导出供其他模块使用。

### Q: 如何创建新系统？
A: 在 `src/core/ecs/systems/` 目录下创建新的系统文件，使用 `defineQuery` 创建查询，然后注册到ECS世界。

### Q: 如何处理组件数据？
A: bitECS使用Structure of Arrays (SoA)存储，通过 `Component.property[entity]` 访问数据。

## 相关文件

- `src/core/ecs/bitECSSystem.js` - ECS系统核心
- `src/core/ecs/systems/MovementSystem.js` - 移动系统示例
- `src/test-bitecs.js` - bitECS功能测试
- `libs/bitECS/` - bitECS库源码

## 更多信息

- [bitECS官方文档](https://github.com/NateTheGreatt/bitECS)
- [ECS架构介绍](https://en.wikipedia.org/wiki/Entity_component_system)
