// src/input/inputManager.js

// 导入 Babylon.js 核心模块
import {
    Vector3,                // 三维向量
    KeyboardEventTypes,     // 键盘事件类型枚举
    Scene                   // 场景，用于注册键盘观察者
} from "@babylonjs/core";

/**
 * InputManager 类负责捕获和管理用户的键盘输入。
 * 它将键盘按键状态转换为规范化的移动向量。
 */
export default class InputManager {
    /**
     * 构造函数，初始化输入管理器。
     * @param {BABYLON.Scene} scene - Babylon.js 场景实例，用于注册键盘观察者。
     */
    constructor(scene) {
        this.scene = scene;
        this.inputMap = {};             // 用于存储当前按下的键的状态，键为小写字符串，值为 boolean
        this.currentMovement = Vector3.Zero(); // 当前帧计算出的移动方向向量
        this.isJumpQueued = false;      // 标记跳跃键是否已按下（用于单次触发）
    }

    /**
     * 初始化输入管理器，设置键盘事件监听器。
     */
    init() {
        // 注册键盘事件观察者
        this.scene.onKeyboardObservable.add((kbInfo) => {
            const key = kbInfo.event.key.toLowerCase(); // 获取按键并转换为小写
            console.log("Key pressed: ", key);
            switch (kbInfo.type) {
                case KeyboardEventTypes.KEYDOWN:
                    this.inputMap[key] = true;
                    // 如果是空格键按下，则标记跳跃为已队列
                    if (key === " " && !this.isJumpQueued) {
                        this.isJumpQueued = true;
                    }
                    break;
                case KeyboardEventTypes.KEYUP:
                    this.inputMap[key] = false;
                    // 如果是空格键松开，则重置跳跃队列状态
                    if (key === " ") {
                        this.isJumpQueued = false;
                    }
                    break;
            }
        });

        console.log("InputManager 初始化完成，键盘事件监听已设置。");
    }

    /**
     * 每帧更新并计算当前的移动方向向量。
     * @returns {BABYLON.Vector3} 规范化的移动方向向量。
     */
    update() {
        // 重置当前移动向量
        this.currentMovement.setAll(0);

        // 根据 inputMap 中的按键状态构建移动向量
        // Z轴代表前进/后退 (W/S)
        if (this.inputMap["w"]) {
            this.currentMovement.z += 1;
        }
        if (this.inputMap["s"]) {
            this.currentMovement.z -= 1;
        }
        // X轴代表左右平移 (A/D)
        if (this.inputMap["a"]) {
            this.currentMovement.x -= 1;
        }
        if (this.inputMap["d"]) {
            this.currentMovement.x += 1;
        }

        // 如果存在对角线移动，归一化向量以确保对角线移动速度不比单轴移动快
        if (this.currentMovement.lengthSquared() > 0.001) { // 使用 lengthSquared() 避免开销大的 length()
            this.currentMovement.normalize();
        }

        return this.currentMovement;
    }

    /**
     * 检查跳跃键是否被按下并队列。
     * @returns {boolean} 如果跳跃键已按下并准备触发，则为 true。
     */
    getJumpInput() {
        // 返回当前跳跃键的状态，并立即重置，确保一次按下只触发一次跳跃
        const jumpStatus = this.isJumpQueued;
        // 如果这里返回true，我们会在使用后立即重置，防止下一帧重复触发
        // 但更好的做法是，在player.js中真正应用跳跃后才重置isJumpQueued
        // 在此处仅返回状态
        return jumpStatus;
    }

    /**
     * 重置跳跃输入状态，通常在玩家成功跳跃后调用。
     */
    resetJumpInput() {
        this.isJumpQueued = false;
    }

    /**
     * 获取指定按键的当前按下状态。
     * @param {string} key - 按键字符（小写）。
     * @returns {boolean} 如果按键当前被按下，则为 true。
     */
    isKeyDown(key) {
        return !!this.inputMap[key.toLowerCase()];
    }
}
