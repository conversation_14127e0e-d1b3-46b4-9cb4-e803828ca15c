

# 开发记录



## 项目模板

[babylonjs-webpack-es6](https://github.com/RaananW/babylonjs-webpack-es6)



## 关于Babylon.js包

BJS发布包括[UMD包](https://doc.babylonjs.com/setup/frameworkPackages/npmSupport)和[ES6包](https://doc.babylonjs.com/setup/frameworkPackages/es6Support/), UMD包将所有模块装到一个文件中, 方便web上一次性导入或快速原型设计, ES6包则分了很多模块, 开发时按需导入, 支持树摇, **在复杂开发中应该使用ES6包**.

参考: [Packing and Shipping](https://babylonjs.medium.com/packing-and-shipping-3778a242e3aa)