<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>场景管理系统演示 - 山海经世界</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            overflow: hidden;
        }

        #renderCanvas {
            width: 100%;
            height: 100vh;
            display: block;
            outline: none;
        }

        .control-panel {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 300px;
            z-index: 1000;
        }

        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            font-size: 18px;
        }

        .scene-info {
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }

        .scene-info p {
            margin: 5px 0;
            font-size: 14px;
        }

        .scene-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .scene-button {
            padding: 10px 15px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .scene-button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .scene-button:active {
            transform: translateY(0);
        }

        .scene-button.active {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }

        .performance-stats {
            font-size: 12px;
            color: #ccc;
        }

        .performance-stats p {
            margin: 3px 0;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            z-index: 2000;
        }

        .loading.hidden {
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .instructions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            max-width: 250px;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <canvas id="renderCanvas"></canvas>
    
    <div class="loading" id="loadingScreen">
        <div class="spinner"></div>
        <h3>正在初始化场景管理系统...</h3>
        <p>请稍候，正在加载山海经世界</p>
    </div>

    <div class="control-panel">
        <h3>🎮 场景管理系统控制台</h3>
        
        <div class="scene-info" id="sceneInfo">
            <p><strong>当前场景:</strong> <span id="currentScene">加载中...</span></p>
            <p><strong>场景类型:</strong> <span id="sceneType">-</span></p>
            <p><strong>创建时间:</strong> <span id="createdAt">-</span></p>
        </div>

        <div class="scene-buttons" id="sceneButtons">
            <!-- 场景按钮将通过JavaScript动态生成 -->
        </div>

        <div class="performance-stats" id="performanceStats">
            <p><strong>性能统计:</strong></p>
            <p>总场景数: <span id="totalScenes">0</span></p>
            <p>预加载场景: <span id="preloadedScenes">0</span></p>
            <p>内存使用: <span id="memoryUsage">-</span></p>
        </div>
    </div>

    <div class="instructions">
        <h4>🎯 操作说明</h4>
        <ul>
            <li>点击场景按钮切换场景</li>
            <li>WASD 移动角色</li>
            <li>鼠标控制视角</li>
            <li>空格键跳跃</li>
            <li>F12 打开开发者工具查看详细信息</li>
        </ul>
    </div>

    <script>
        // 等待场景管理系统初始化完成
        window.addEventListener('load', async () => {
            try {
                // 等待场景系统初始化
                await waitForSceneManager();
                
                // 隐藏加载屏幕
                document.getElementById('loadingScreen').classList.add('hidden');
                
                // 初始化控制面板
                initializeControlPanel();
                
                // 开始更新循环
                startUpdateLoop();
                
                console.log('🎮 场景管理系统演示页面已就绪');
                
            } catch (error) {
                console.error('场景管理系统初始化失败:', error);
                document.getElementById('loadingScreen').innerHTML = `
                    <h3>❌ 初始化失败</h3>
                    <p>${error.message}</p>
                    <button onclick="location.reload()">重新加载</button>
                `;
            }
        });

        // 等待场景管理器可用
        function waitForSceneManager() {
            return new Promise((resolve, reject) => {
                const checkInterval = setInterval(() => {
                    if (window.sceneManager) {
                        clearInterval(checkInterval);
                        resolve(window.sceneManager);
                    }
                }, 100);
                
                // 10秒超时
                setTimeout(() => {
                    clearInterval(checkInterval);
                    reject(new Error('场景管理器初始化超时'));
                }, 10000);
            });
        }

        // 初始化控制面板
        function initializeControlPanel() {
            const sceneManager = window.sceneManager;
            const sceneFactory = window.sceneFactory;
            
            if (!sceneManager || !sceneFactory) {
                throw new Error('场景管理器或场景工厂未找到');
            }

            // 生成场景按钮
            generateSceneButtons();
            
            // 更新场景信息
            updateSceneInfo();
            
            // 监听场景切换事件
            sceneManager.on('sceneTransitionComplete', (data) => {
                updateSceneInfo();
                updateSceneButtons();
                console.log(`✅ 场景切换完成: ${data.to}`);
            });
        }

        // 生成场景按钮
        function generateSceneButtons() {
            const sceneFactory = window.sceneFactory;
            const sceneButtonsContainer = document.getElementById('sceneButtons');
            
            const availableScenes = sceneFactory.getAvailableSceneTypes();
            
            sceneButtonsContainer.innerHTML = '';
            
            availableScenes.forEach(sceneType => {
                const button = document.createElement('button');
                button.className = 'scene-button';
                button.textContent = sceneType.name;
                button.title = sceneType.description;
                button.onclick = () => switchToScene(sceneType.type);
                button.dataset.sceneType = sceneType.type;
                
                sceneButtonsContainer.appendChild(button);
            });
        }

        // 切换场景
        async function switchToScene(sceneType) {
            const sceneManager = window.sceneManager;
            const sceneFactory = window.sceneFactory;
            
            try {
                // 显示加载状态
                const button = document.querySelector(`[data-scene-type="${sceneType}"]`);
                const originalText = button.textContent;
                button.textContent = '切换中...';
                button.disabled = true;
                
                // 创建场景配置
                const sceneConfig = {
                    id: `${sceneType}-scene-${Date.now()}`,
                    type: sceneType,
                    options: {}
                };
                
                // 创建场景（如果不存在）
                if (!sceneManager.hasScene(sceneConfig.id)) {
                    const canvas = document.getElementById('renderCanvas');
                    await sceneManager.createScene(sceneConfig, canvas);
                }
                
                // 切换到场景
                await sceneManager.switchScene(sceneConfig.id);
                
                // 恢复按钮状态
                button.textContent = originalText;
                button.disabled = false;
                
            } catch (error) {
                console.error(`场景切换失败: ${sceneType}`, error);
                alert(`场景切换失败: ${error.message}`);
                
                // 恢复按钮状态
                const button = document.querySelector(`[data-scene-type="${sceneType}"]`);
                button.textContent = button.title;
                button.disabled = false;
            }
        }

        // 更新场景信息
        function updateSceneInfo() {
            const sceneManager = window.sceneManager;
            const currentScene = sceneManager.getCurrentScene();
            
            if (currentScene) {
                document.getElementById('currentScene').textContent = currentScene.id;
                document.getElementById('sceneType').textContent = currentScene.type;
                document.getElementById('createdAt').textContent = 
                    new Date(currentScene.createdAt).toLocaleTimeString();
            } else {
                document.getElementById('currentScene').textContent = '无';
                document.getElementById('sceneType').textContent = '-';
                document.getElementById('createdAt').textContent = '-';
            }
        }

        // 更新场景按钮状态
        function updateSceneButtons() {
            const sceneManager = window.sceneManager;
            const currentScene = sceneManager.getCurrentScene();
            
            document.querySelectorAll('.scene-button').forEach(button => {
                button.classList.remove('active');
                if (currentScene && button.dataset.sceneType === currentScene.type) {
                    button.classList.add('active');
                }
            });
        }

        // 更新性能统计
        function updatePerformanceStats() {
            const sceneManager = window.sceneManager;
            
            if (sceneManager) {
                const stats = sceneManager.getPerformanceStats();
                
                document.getElementById('totalScenes').textContent = stats.totalScenes;
                document.getElementById('preloadedScenes').textContent = stats.preloadedScenes;
                
                if (stats.memoryUsage) {
                    document.getElementById('memoryUsage').textContent = 
                        `${stats.memoryUsage.used}MB / ${stats.memoryUsage.total}MB`;
                } else {
                    document.getElementById('memoryUsage').textContent = '不可用';
                }
            }
        }

        // 开始更新循环
        function startUpdateLoop() {
            setInterval(() => {
                updatePerformanceStats();
            }, 1000);
        }
    </script>
</body>
</html>
