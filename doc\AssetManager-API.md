# 资源管理系统 (AssetManager) API 文档

## 概述

资源管理系统是《Cantos》MMORPG项目的核心组件之一，负责统一管理游戏中的所有资源，包括3D模型、纹理、音频和配置文件。系统提供异步加载、智能缓存、进度跟踪和内存管理等功能。

## 核心特性

- **异步资源加载**: 支持多种资源类型的异步加载
- **智能缓存系统**: 基于LRU算法的缓存管理
- **进度跟踪**: 详细的加载进度监控
- **内存管理**: 自动垃圾回收和内存优化
- **错误处理**: 完善的错误处理和重试机制
- **批量预加载**: 支持资源列表的批量预加载

## 快速开始

### 初始化

```javascript
import { initializeGlobalAssetManager, AssetPresets } from './core/assets/index.js';

// 使用预设配置初始化全局资源管理器
const assetManager = initializeGlobalAssetManager(AssetPresets.STANDARD);

// 或者使用自定义配置
const customAssetManager = initializeGlobalAssetManager({
    maxCacheSize: 200 * 1024 * 1024, // 200MB
    enableAutoGC: true,
    gcThreshold: 0.8,
    retryAttempts: 3
});
```

### 基本使用

```javascript
// 加载3D模型
const modelAsset = await assetManager.loadModel('models/character.glb', {
    scene: babylonScene,
    scaleFactor: 1.5,
    enableCollisions: true
});

// 加载纹理
const textureAsset = await assetManager.loadTexture('textures/ground.jpg', {
    scene: babylonScene,
    noMipmap: false
});

// 加载音频
const soundAsset = await assetManager.loadSound('sounds/background.mp3', {
    scene: babylonScene,
    loop: true,
    volume: 0.5
});

// 加载配置文件
const configAsset = await assetManager.loadConfig('config/game-settings.json');
```

## API 参考

### AssetManager 类

#### 构造函数

```javascript
new AssetManager(options)
```

**参数:**
- `options` (Object): 配置选项
  - `maxCacheSize` (number): 最大缓存大小（字节），默认100MB
  - `enableAutoGC` (boolean): 启用自动垃圾回收，默认true
  - `gcThreshold` (number): GC触发阈值（0-1），默认0.8
  - `retryAttempts` (number): 重试次数，默认3
  - `retryDelay` (number): 重试延迟（毫秒），默认1000

#### 主要方法

##### loadModel(url, options)

加载3D模型资源。

**参数:**
- `url` (string): 模型文件URL
- `options` (Object): 加载选项
  - `scene` (Scene): Babylon.js场景对象（必需）
  - `importMeshes` (Array): 要导入的网格名称数组
  - `scaleFactor` (number): 缩放因子，默认1
  - `position` (Vector3): 位置
  - `rotation` (Vector3): 旋转
  - `enableCollisions` (boolean): 启用碰撞，默认false
  - `enablePhysics` (boolean): 启用物理，默认false

**返回值:** Promise<Object> - 模型资源对象

**示例:**
```javascript
const model = await assetManager.loadModel('models/dragon.glb', {
    scene: scene,
    scaleFactor: 2.0,
    position: new Vector3(0, 0, 0),
    enableCollisions: true
});

// 访问加载的网格
model.meshes.forEach(mesh => {
    console.log('加载的网格:', mesh.name);
});
```

##### loadTexture(url, options)

加载纹理资源。

**参数:**
- `url` (string): 纹理文件URL
- `options` (Object): 加载选项
  - `scene` (Scene): Babylon.js场景对象（必需）
  - `noMipmap` (boolean): 不生成mipmap，默认false
  - `invertY` (boolean): 反转Y轴，默认true
  - `samplingMode` (number): 采样模式

**返回值:** Promise<Object> - 纹理资源对象

**示例:**
```javascript
const texture = await assetManager.loadTexture('textures/stone.jpg', {
    scene: scene,
    noMipmap: false
});

// 应用纹理到材质
material.diffuseTexture = texture.texture;
```

##### loadSound(url, options)

加载音频资源。

**参数:**
- `url` (string): 音频文件URL
- `options` (Object): 加载选项
  - `scene` (Scene): Babylon.js场景对象
  - `autoplay` (boolean): 自动播放，默认false
  - `loop` (boolean): 循环播放，默认false
  - `volume` (number): 音量（0-1），默认1.0
  - `spatialSound` (boolean): 空间音效，默认false
  - `maxDistance` (number): 最大距离，默认100

**返回值:** Promise<Object> - 音频资源对象

**示例:**
```javascript
const bgMusic = await assetManager.loadSound('audio/background.mp3', {
    scene: scene,
    loop: true,
    volume: 0.3
});

// 控制音频播放
bgMusic.play();
bgMusic.pause();
bgMusic.setVolume(0.5);
```

##### loadConfig(url, options)

加载配置文件。

**参数:**
- `url` (string): 配置文件URL
- `options` (Object): 加载选项
  - `format` (string): 格式（'auto', 'json', 'xml'），默认'auto'
  - `timeout` (number): 超时时间（毫秒），默认30000

**返回值:** Promise<Object> - 配置数据对象

**示例:**
```javascript
const gameConfig = await assetManager.loadConfig('config/levels.json');
const levelData = gameConfig.data;

console.log('关卡数量:', levelData.levels.length);
```

##### preloadAssets(assetList, options)

批量预加载资源。

**参数:**
- `assetList` (Array): 资源列表
- `options` (Object): 预加载选项
  - `concurrent` (number): 并发加载数量，默认3
  - `onProgress` (Function): 进度回调函数

**返回值:** Promise<Object> - 预加载结果

**示例:**
```javascript
const assetList = [
    { url: 'models/character1.glb', type: 'model' },
    { url: 'models/character2.glb', type: 'model' },
    { url: 'textures/ui-background.jpg', type: 'texture' }
];

const result = await assetManager.preloadAssets(assetList, {
    concurrent: 2,
    onProgress: (progress) => {
        console.log(`预加载进度: ${progress.completed}/${progress.total}`);
    }
});

console.log(`预加载完成: 成功 ${result.loaded.length}, 失败 ${result.failed.length}`);
```

##### getAsset(url, options)

获取已缓存的资源。

**参数:**
- `url` (string): 资源URL
- `options` (Object): 选项（用于生成缓存键）

**返回值:** Object|null - 资源对象或null

##### releaseAsset(url, options)

释放资源。

**参数:**
- `url` (string): 资源URL
- `options` (Object): 选项

##### getLoadingProgress()

获取当前加载进度。

**返回值:** Object - 进度信息

##### getStats()

获取统计信息。

**返回值:** Object - 统计数据

## 事件系统

AssetManager继承自EventEmitter，支持以下事件：

### 事件列表

- `assetLoadStart`: 资源开始加载
- `assetLoaded`: 资源加载完成
- `assetLoadError`: 资源加载失败
- `assetLoadProgress`: 资源加载进度更新
- `assetReleased`: 资源被释放
- `preloadComplete`: 预加载完成
- `garbageCollected`: 垃圾回收完成

### 事件监听示例

```javascript
// 监听资源加载完成
assetManager.on('assetLoaded', (data) => {
    console.log(`资源加载完成: ${data.url}`);
    console.log(`加载时间: ${data.loadTime}ms`);
    console.log(`来自缓存: ${data.fromCache}`);
});

// 监听加载进度
assetManager.on('assetLoadProgress', (data) => {
    console.log(`${data.url} 加载进度: ${data.progress}%`);
});

// 监听加载错误
assetManager.on('assetLoadError', (data) => {
    console.error(`资源加载失败: ${data.url}`, data.error);
});

// 监听垃圾回收
assetManager.on('garbageCollected', (data) => {
    console.log(`垃圾回收完成，释放空间: ${data.freedSpace / 1024 / 1024}MB`);
});
```

## 配置预设

系统提供了几种预设配置：

### AssetPresets.LOW_MEMORY
适用于内存受限的设备：
- 缓存大小: 50MB
- GC阈值: 70%
- 重试次数: 2

### AssetPresets.STANDARD
标准配置（推荐）：
- 缓存大小: 100MB
- GC阈值: 80%
- 重试次数: 3

### AssetPresets.HIGH_PERFORMANCE
高性能配置：
- 缓存大小: 200MB
- GC阈值: 90%
- 重试次数: 3
- 启用预加载

### AssetPresets.DEVELOPMENT
开发模式配置：
- 缓存大小: 50MB
- 禁用自动GC
- 重试次数: 1

## 最佳实践

### 1. 资源组织

```javascript
// 按类型组织资源
const ASSETS = {
    MODELS: {
        PLAYER: 'models/player.glb',
        ENEMY: 'models/enemy.glb'
    },
    TEXTURES: {
        UI_BACKGROUND: 'textures/ui-bg.jpg',
        GROUND: 'textures/ground.jpg'
    },
    SOUNDS: {
        BGM: 'audio/background.mp3',
        SFX_JUMP: 'audio/jump.wav'
    },
    CONFIGS: {
        GAME_SETTINGS: 'config/game.json',
        LEVEL_DATA: 'config/levels.json'
    }
};
```

### 2. 预加载策略

```javascript
// 游戏启动时预加载核心资源
const coreAssets = [
    { url: ASSETS.CONFIGS.GAME_SETTINGS, type: 'config' },
    { url: ASSETS.TEXTURES.UI_BACKGROUND, type: 'texture' },
    { url: ASSETS.SOUNDS.BGM, type: 'sound' }
];

await assetManager.preloadAssets(coreAssets);

// 关卡切换时预加载关卡资源
const levelAssets = getLevelAssets(currentLevel);
assetManager.preloadAssets(levelAssets, { concurrent: 5 });
```

### 3. 内存管理

```javascript
// 监控内存使用
setInterval(() => {
    const stats = assetManager.getStats();
    if (stats.cacheUsage > 0.9) {
        console.warn('缓存使用率过高:', stats.usagePercent);
    }
}, 10000);

// 场景切换时释放不需要的资源
function switchToLevel(newLevel) {
    // 释放旧关卡资源
    oldLevelAssets.forEach(asset => {
        assetManager.releaseAsset(asset.url);
    });
    
    // 加载新关卡资源
    const newAssets = getLevelAssets(newLevel);
    return assetManager.preloadAssets(newAssets);
}
```

### 4. 错误处理

```javascript
// 注册错误处理器
assetManager.registerErrorHandler('model', async (url, type, options, error) => {
    console.warn(`模型加载失败，使用默认模型: ${url}`);
    return assetManager.loadModel('models/default.glb', options);
});

// 使用try-catch处理加载错误
try {
    const model = await assetManager.loadModel('models/special.glb', { scene });
} catch (error) {
    console.error('特殊模型加载失败，使用默认模型');
    const fallbackModel = await assetManager.loadModel('models/default.glb', { scene });
}
```

## 性能优化建议

1. **合理设置缓存大小**: 根据目标设备的内存情况调整缓存大小
2. **使用预加载**: 在用户操作前预加载可能需要的资源
3. **及时释放资源**: 不再需要的资源应及时释放
4. **监控内存使用**: 定期检查内存使用情况，避免内存泄漏
5. **优化资源格式**: 使用压缩格式减少资源大小
6. **批量加载**: 使用批量预加载提高加载效率

## 故障排除

### 常见问题

1. **资源加载失败**
   - 检查资源URL是否正确
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

2. **内存使用过高**
   - 检查是否有资源泄漏
   - 调整缓存大小和GC阈值
   - 及时释放不需要的资源

3. **加载速度慢**
   - 使用预加载策略
   - 增加并发加载数量
   - 优化资源大小和格式

### 调试工具

```javascript
// 查看缓存状态
console.log('缓存统计:', assetManager.getStats());

// 查看加载进度
console.log('加载进度:', assetManager.getLoadingProgress());

// 查看所有缓存项
console.log('缓存项:', assetManager.cache.getAllItemsInfo());
```
