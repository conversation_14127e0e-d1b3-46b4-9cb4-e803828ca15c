// src/core/assets/AssetManager.js
// 资源管理系统 - 负责异步资源加载、缓存管理、内存管理和加载进度追踪

import { EventEmitter } from "../eventEmitter/index.js";
import { AssetLoaders } from "./AssetLoaders.js";
import { AssetCache } from "./AssetCache.js";
import { LoadingProgressManager } from "./LoadingProgressManager.js";

/**
 * 资源类型枚举
 */
export const AssetType = {
    MODEL: 'model',
    TEXTURE: 'texture', 
    SOUND: 'sound',
    CONFIG: 'config',
    UNKNOWN: 'unknown'
};

/**
 * 资源状态枚举
 */
export const AssetStatus = {
    PENDING: 'pending',      // 等待加载
    LOADING: 'loading',      // 正在加载
    LOADED: 'loaded',        // 加载完成
    ERROR: 'error',          // 加载失败
    DISPOSED: 'disposed'     // 已释放
};

/**
 * 资源管理系统类
 * 提供统一的资源加载、缓存和管理接口
 */
export class AssetManager extends EventEmitter {
    constructor(options = {}) {
        super();

        // 配置选项
        this.options = {
            maxCacheSize: options.maxCacheSize || 100 * 1024 * 1024, // 100MB默认缓存大小
            enableAutoGC: options.enableAutoGC !== false, // 默认启用自动垃圾回收
            gcThreshold: options.gcThreshold || 0.8, // GC触发阈值（缓存使用率）
            retryAttempts: options.retryAttempts || 3, // 重试次数
            retryDelay: options.retryDelay || 1000, // 重试延迟（毫秒）
            enablePreloading: options.enablePreloading !== false, // 启用预加载
            ...options
        };

        // 核心组件
        this.loaders = new AssetLoaders(this);
        this.cache = new AssetCache(this.options.maxCacheSize);
        this.progressManager = new LoadingProgressManager();

        // 资源注册表 - 存储所有资源的元数据
        this.assetRegistry = new Map();

        // 加载队列管理
        this.loadingQueue = new Map(); // 正在加载的资源
        this.preloadQueue = []; // 预加载队列

        // 性能统计
        this.stats = {
            totalLoaded: 0,
            totalFailed: 0,
            cacheHits: 0,
            cacheMisses: 0,
            memoryUsage: 0,
            loadingTime: 0
        };

        // 错误处理
        this.errorHandlers = new Map();

        console.log('资源管理系统已初始化', this.options);
    }

    /**
     * 加载3D模型资源
     * @param {string} url - 模型文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载的模型资源
     */
    async loadModel(url, options = {}) {
        return this._loadAsset(url, AssetType.MODEL, options);
    }

    /**
     * 加载纹理资源
     * @param {string} url - 纹理文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载的纹理资源
     */
    async loadTexture(url, options = {}) {
        return this._loadAsset(url, AssetType.TEXTURE, options);
    }

    /**
     * 加载音频资源
     * @param {string} url - 音频文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载的音频资源
     */
    async loadSound(url, options = {}) {
        return this._loadAsset(url, AssetType.SOUND, options);
    }

    /**
     * 加载配置文件
     * @param {string} url - 配置文件URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载的配置数据
     */
    async loadConfig(url, options = {}) {
        return this._loadAsset(url, AssetType.CONFIG, options);
    }

    /**
     * 通用资源加载方法
     * @private
     */
    async _loadAsset(url, type, options = {}) {
        const assetId = this._generateAssetId(url, options);
        
        // 检查缓存
        if (this.cache.has(assetId)) {
            this.stats.cacheHits++;
            console.log(`从缓存加载资源: ${url}`);
            
            const cachedAsset = this.cache.get(assetId);
            this.emit('assetLoaded', { url, type, asset: cachedAsset, fromCache: true });
            return cachedAsset;
        }

        this.stats.cacheMisses++;

        // 检查是否正在加载
        if (this.loadingQueue.has(assetId)) {
            console.log(`资源正在加载中，等待完成: ${url}`);
            return this.loadingQueue.get(assetId);
        }

        // 开始加载
        const loadPromise = this._performAssetLoad(url, type, options, assetId);
        this.loadingQueue.set(assetId, loadPromise);

        try {
            const asset = await loadPromise;
            return asset;
        } finally {
            this.loadingQueue.delete(assetId);
        }
    }

    /**
     * 执行实际的资源加载
     * @private
     */
    async _performAssetLoad(url, type, options, assetId) {
        const startTime = performance.now();
        
        // 注册资源到注册表
        this._registerAsset(assetId, url, type, AssetStatus.LOADING, options);

        // 创建加载进度跟踪
        const progressId = this.progressManager.createProgress(url, type);

        try {
            console.log(`开始加载资源: ${url} (类型: ${type})`);
            
            // 触发加载开始事件
            this.emit('assetLoadStart', { url, type, assetId, options });

            // 使用对应的加载器加载资源
            const asset = await this.loaders.load(url, type, options, (progress) => {
                this.progressManager.updateProgress(progressId, progress);
                this.emit('assetLoadProgress', { url, type, progress });
            });

            // 加载成功处理
            const loadTime = performance.now() - startTime;
            this.stats.totalLoaded++;
            this.stats.loadingTime += loadTime;

            // 存储到缓存
            this.cache.set(assetId, asset, this._calculateAssetSize(asset));
            
            // 更新注册表状态
            this._updateAssetStatus(assetId, AssetStatus.LOADED);

            // 完成进度跟踪
            this.progressManager.completeProgress(progressId);

            console.log(`资源加载完成: ${url} (耗时: ${loadTime.toFixed(2)}ms)`);

            // 触发加载完成事件
            this.emit('assetLoaded', { url, type, asset, loadTime, fromCache: false });

            // 检查是否需要垃圾回收
            if (this.options.enableAutoGC) {
                this._checkAndPerformGC();
            }

            return asset;

        } catch (error) {
            // 加载失败处理
            this.stats.totalFailed++;
            this._updateAssetStatus(assetId, AssetStatus.ERROR);
            this.progressManager.failProgress(progressId, error);

            console.error(`资源加载失败: ${url}`, error);

            // 触发加载失败事件
            this.emit('assetLoadError', { url, type, error, assetId });

            // 尝试错误恢复
            const recoveredAsset = await this._handleLoadError(url, type, options, error);
            if (recoveredAsset) {
                return recoveredAsset;
            }

            throw error;
        }
    }

    /**
     * 预加载资源列表
     * @param {Array} assetList - 资源列表
     * @param {Object} options - 预加载选项
     * @returns {Promise<Object>} 预加载结果
     */
    async preloadAssets(assetList, options = {}) {
        console.log(`开始预加载 ${assetList.length} 个资源`);
        
        const { 
            concurrent = 3, // 并发加载数量
            priority = 'normal', // 优先级
            onProgress = null // 进度回调
        } = options;

        const results = {
            loaded: [],
            failed: [],
            total: assetList.length
        };

        // 创建批量进度跟踪
        const batchProgressId = this.progressManager.createBatchProgress(assetList);

        try {
            // 分批并发加载
            const batches = this._createLoadingBatches(assetList, concurrent);
            
            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                const batchPromises = batch.map(async (assetInfo) => {
                    try {
                        const asset = await this._loadAsset(
                            assetInfo.url, 
                            assetInfo.type || this._detectAssetType(assetInfo.url),
                            assetInfo.options || {}
                        );
                        results.loaded.push({ ...assetInfo, asset });
                        return { success: true, assetInfo, asset };
                    } catch (error) {
                        results.failed.push({ ...assetInfo, error });
                        return { success: false, assetInfo, error };
                    }
                });

                // 等待当前批次完成
                const batchResults = await Promise.allSettled(batchPromises);
                
                // 更新批量进度
                const completedCount = results.loaded.length + results.failed.length;
                const progress = completedCount / results.total;
                this.progressManager.updateBatchProgress(batchProgressId, progress);

                // 调用进度回调
                if (onProgress) {
                    onProgress({
                        completed: completedCount,
                        total: results.total,
                        progress: progress,
                        batch: i + 1,
                        totalBatches: batches.length
                    });
                }

                console.log(`预加载批次 ${i + 1}/${batches.length} 完成`);
            }

            // 完成批量进度跟踪
            this.progressManager.completeBatchProgress(batchProgressId);

            console.log(`预加载完成: 成功 ${results.loaded.length}, 失败 ${results.failed.length}`);

            // 触发预加载完成事件
            this.emit('preloadComplete', results);

            return results;

        } catch (error) {
            this.progressManager.failBatchProgress(batchProgressId, error);
            console.error('预加载过程中发生错误:', error);
            throw error;
        }
    }

    /**
     * 获取资源
     * @param {string} url - 资源URL
     * @param {Object} options - 选项
     * @returns {Object|null} 资源对象或null
     */
    getAsset(url, options = {}) {
        const assetId = this._generateAssetId(url, options);
        return this.cache.get(assetId) || null;
    }

    /**
     * 释放资源
     * @param {string} url - 资源URL
     * @param {Object} options - 选项
     */
    releaseAsset(url, options = {}) {
        const assetId = this._generateAssetId(url, options);
        
        if (this.cache.has(assetId)) {
            const asset = this.cache.get(assetId);
            
            // 调用资源的dispose方法（如果存在）
            if (asset && typeof asset.dispose === 'function') {
                asset.dispose();
            }

            // 从缓存中移除
            this.cache.delete(assetId);
            
            // 更新注册表状态
            this._updateAssetStatus(assetId, AssetStatus.DISPOSED);

            console.log(`资源已释放: ${url}`);
            
            // 触发资源释放事件
            this.emit('assetReleased', { url, assetId });
        }
    }

    /**
     * 获取加载进度
     * @returns {Object} 加载进度信息
     */
    getLoadingProgress() {
        return this.progressManager.getOverallProgress();
    }

    /**
     * 生成资源ID
     * @private
     */
    _generateAssetId(url, options) {
        const optionsHash = JSON.stringify(options);
        return `${url}#${btoa(optionsHash)}`;
    }

    /**
     * 注册资源到注册表
     * @private
     */
    _registerAsset(assetId, url, type, status, options) {
        this.assetRegistry.set(assetId, {
            id: assetId,
            url,
            type,
            status,
            options,
            createdAt: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 0
        });
    }

    /**
     * 更新资源状态
     * @private
     */
    _updateAssetStatus(assetId, status) {
        const assetInfo = this.assetRegistry.get(assetId);
        if (assetInfo) {
            assetInfo.status = status;
            assetInfo.lastAccessed = Date.now();
            assetInfo.accessCount++;
        }
    }

    /**
     * 检测资源类型
     * @private
     */
    _detectAssetType(url) {
        const extension = url.split('.').pop().toLowerCase();
        
        if (['glb', 'gltf', 'babylon'].includes(extension)) {
            return AssetType.MODEL;
        } else if (['jpg', 'jpeg', 'png', 'dds', 'ktx'].includes(extension)) {
            return AssetType.TEXTURE;
        } else if (['mp3', 'wav', 'ogg'].includes(extension)) {
            return AssetType.SOUND;
        } else if (['json', 'xml'].includes(extension)) {
            return AssetType.CONFIG;
        }
        
        return AssetType.UNKNOWN;
    }

    /**
     * 计算资源大小（估算）
     * @private
     */
    _calculateAssetSize(asset) {
        // 这是一个简化的大小估算，实际实现可能需要更复杂的逻辑
        if (!asset) return 0;
        
        try {
            return JSON.stringify(asset).length;
        } catch {
            return 1024; // 默认1KB
        }
    }

    /**
     * 创建加载批次
     * @private
     */
    _createLoadingBatches(assetList, batchSize) {
        const batches = [];
        for (let i = 0; i < assetList.length; i += batchSize) {
            batches.push(assetList.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * 检查并执行垃圾回收
     * @private
     */
    _checkAndPerformGC() {
        const cacheUsage = this.cache.getCurrentSize() / this.cache.getMaxSize();
        
        if (cacheUsage > this.options.gcThreshold) {
            console.log(`缓存使用率 ${(cacheUsage * 100).toFixed(1)}% 超过阈值，开始垃圾回收`);
            this._performGarbageCollection();
        }
    }

    /**
     * 执行垃圾回收
     * @private
     */
    _performGarbageCollection() {
        const beforeSize = this.cache.getCurrentSize();
        const freedSpace = this.cache.performLRUCleanup();
        
        console.log(`垃圾回收完成，释放空间: ${(freedSpace / 1024 / 1024).toFixed(2)}MB`);
        
        // 触发垃圾回收事件
        this.emit('garbageCollected', {
            beforeSize,
            afterSize: this.cache.getCurrentSize(),
            freedSpace
        });
    }

    /**
     * 处理加载错误
     * @private
     */
    async _handleLoadError(url, type, options, error) {
        // 检查是否有注册的错误处理器
        const errorHandler = this.errorHandlers.get(type);
        if (errorHandler) {
            try {
                return await errorHandler(url, type, options, error);
            } catch (handlerError) {
                console.error('错误处理器执行失败:', handlerError);
            }
        }

        // 默认重试逻辑
        if (options.retryCount < this.options.retryAttempts) {
            const retryOptions = { ...options, retryCount: (options.retryCount || 0) + 1 };
            console.log(`重试加载资源 (${retryOptions.retryCount}/${this.options.retryAttempts}): ${url}`);
            
            // 延迟后重试
            await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
            return this._loadAsset(url, type, retryOptions);
        }

        return null;
    }

    /**
     * 注册错误处理器
     * @param {string} assetType - 资源类型
     * @param {Function} handler - 错误处理函数
     */
    registerErrorHandler(assetType, handler) {
        this.errorHandlers.set(assetType, handler);
        console.log(`已注册 ${assetType} 类型的错误处理器`);
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计数据
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.getCurrentSize(),
            cacheMaxSize: this.cache.getMaxSize(),
            cacheUsage: this.cache.getCurrentSize() / this.cache.getMaxSize(),
            registeredAssets: this.assetRegistry.size,
            loadingAssets: this.loadingQueue.size
        };
    }

    /**
     * 清理资源管理器
     */
    dispose() {
        console.log('开始清理资源管理系统...');

        // 停止所有正在进行的加载
        this.loadingQueue.clear();

        // 清理缓存
        this.cache.clear();

        // 清理注册表
        this.assetRegistry.clear();

        // 清理进度管理器
        this.progressManager.dispose();

        // 清理事件监听器
        this.removeAllListeners();

        console.log('资源管理系统清理完成');
    }
}

export default AssetManager;
