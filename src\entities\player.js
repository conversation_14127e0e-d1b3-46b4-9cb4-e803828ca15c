// src/entities/player.js

// 导入 Babylon.js 核心模块
import {
    MeshBuilder,            // 用于构建几何网格
    StandardMaterial,       // 标准材质
    Color3,                 // RGB 颜色
    Vector3,                // 三维向量
    Quaternion,             // 四元数（用于表示旋转）
    TransformNode           // 基础的变换节点，这里 Player 类本身就是管理一个 Mesh
} from "@babylonjs/core";

// 导入 Babylon.js 物理引擎 v2 模块
import { PhysicsBody } from "@babylonjs/core/Physics/v2/physicsBody";
import { PhysicsShapeCapsule } from "@babylonjs/core/Physics/v2/physicsShape";
import { PhysicsMotionType } from "@babylonjs/core/Physics/v2/IPhysicsEnginePlugin";

/**
 * Player 类代表游戏中的一个玩家实体，可以是本地玩家或远程玩家。
 * 它包含玩家的视觉模型、物理属性和基本的运动逻辑。
 */
export default class Player {
    /**
     * 构造函数，创建一个新的玩家实例。
     * @param {string} id - 玩家的唯一标识符。
     * @param {BABYLON.Scene} scene - Babylon.js 场景实例。
     * @param {BABYLON.Vector3} [initialPosition=Vector3.Zero()] - 玩家的初始位置（玩家模型底部中心）。
     * @param {boolean} [isLocalPlayer=false] - 是否是本地玩家（当前客户端控制的玩家）。
     */
    constructor(id, scene, initialPosition = Vector3.Zero(), isLocalPlayer = false) {
        this.id = id;
        this.scene = scene;
        this.isLocalPlayer = isLocalPlayer;

        // --- 玩家模型 (Placeholder) ---
        // 定义胶囊体的高度和半径，与物理形状匹配
        const capsuleHeight = 2.5;
        const capsuleRadius = 0.5;

        // 创建一个胶囊体网格作为玩家的占位符模型
        // 胶囊体默认原点在中心
        this.mesh = MeshBuilder.CreateCapsule(`playerMesh-${this.id}`, { height: capsuleHeight, radius: capsuleRadius }, scene);

        // 创建材质并设置颜色：本地玩家为亮蓝色，远程玩家为柔和的灰色
        const playerMaterial = new StandardMaterial(`playerMaterial-${this.id}`, scene);
        if (this.isLocalPlayer) {
            playerMaterial.diffuseColor = new Color3(0.2, 0.4, 0.8); // 亮蓝色
            playerMaterial.emissiveColor = new Color3(0.1, 0.2, 0.4); // 自发光增加视觉效果
        } else {
            playerMaterial.diffuseColor = new Color3(0.5, 0.5, 0.5); // 柔和的灰色
        }
        this.mesh.material = playerMaterial;

        // 将网格的底部移动到 initialPosition 的 Y 坐标，使其“站立”在地面上
        this.mesh.position.copyFrom(initialPosition);
        this.mesh.position.y += capsuleHeight / 2; // 将胶囊体模型的中心上移一半高度

        // 初始化网格的旋转四元数，确保后续物理和旋转操作基于四元数
        this.mesh.rotationQuaternion = Quaternion.Identity();

        // --- 物理身体 ---
        // 创建一个动态物理体，关联到玩家网格
        // PhysicsMotionType.DYNAMIC: 受力影响并能与其他物理体交互
        this.physicsBody = new PhysicsBody(this.mesh, PhysicsMotionType.DYNAMIC, false, scene);

        // 创建一个胶囊体物理碰撞形状，与网格尺寸匹配
        // pointA 和 pointB 定义了胶囊体圆柱部分的两个端点的中心（相对网格局部原点）
        // 对于一个中心在 (0,0,0) 的胶囊，总高 2.5，半径 0.5，其圆柱部分高度为 1.5。
        // 所以下端点在 y = -0.75，上端点在 y = 0.75。
        const capsuleShape = new PhysicsShapeCapsule(
            new Vector3(0, -0.75, 0),    // Point A (胶囊体底部球体中心，相对于网格局部原点)
            new Vector3(0, 0.75, 0),     // Point B (胶囊体顶部球体中心，相对于网格局部原点)
            capsuleRadius,               // 半径
            scene
        );
        this.physicsBody.shape = capsuleShape;

        // 设置物理材质属性
        this.physicsBody.material = {
            friction: 0.5,      // 摩擦力
            restitution: 0.1,   // 弹性（反弹力）
            mass: 70            // 质量（单位：公斤），影响受力后的行为
        };

        // 重要：为了防止玩家角色因物理碰撞而意外旋转（例如摔倒），将惯性矩设置为零。
        // 这意味着物理引擎不会计算和应用旋转力矩，玩家将保持直立。
        this.physicsBody.setMassProperties({ inertia: new Vector3(0, 0, 0) });

        // --- 玩家状态和输入 ---
        this.movementSpeed = 4.0;       // 基础移动速度（单位：米/秒）
        this.rotationSpeed = Math.PI * 3; // 旋转速度（单位：弧度/秒）

        this.currentMovementInput = Vector3.Zero(); // 当前玩家的移动输入向量 (x, 0, z)
        this.desiredRotationY = 0;                  // 玩家Y轴的期望旋转角度（弧度）
    }

    /**
     * 每帧更新玩家的状态，包括移动和旋转。
     * 仅本地玩家会根据输入进行更新，远程玩家的更新通常通过服务器同步。
     * @param {number} deltaTime - 距离上一帧的时间间隔（单位：秒）。
     */
    update(deltaTime) {
        if (this.isLocalPlayer) {
            // 检查是否有有效的移动输入
            if (this.currentMovementInput.lengthSquared() > 0.001) { // 避免微小浮点数导致不必要的计算
                // 获取当前物理体的线性速度（主要为了保留Y轴速度，例如跳跃或重力）
                const currentLinearVelocity = this.physicsBody.getLinearVelocity();

                // 计算目标水平速度：将输入向量归一化后乘以移动速度
                // 注意：这里假设 currentMovementInput 已经是世界轴向的 (x,0,z) 移动方向
                const targetHorizontalVelocity = this.currentMovementInput.normalize().scale(this.movementSpeed);

                // 设置物理体的线性速度，只更新 XZ 平面速度，保留 Y 轴速度
                this.physicsBody.setLinearVelocity(new Vector3(
                    targetHorizontalVelocity.x,
                    currentLinearVelocity.y, // 保持重力或跳跃造成的 Y 轴速度
                    targetHorizontalVelocity.z
                ));

                // 平滑旋转玩家模型到期望的方向
                // 首先，计算目标四元数
                const targetQuaternion = Quaternion.FromEulerAngles(0, this.desiredRotationY, 0);

                // 使用 Slerp (球面线性插值) 平滑地旋转网格
                // rotationSpeed * deltaTime 决定了插值速度
                this.mesh.rotationQuaternion = Quaternion.Slerp(
                    this.mesh.rotationQuaternion,
                    targetQuaternion,
                    this.rotationSpeed * deltaTime // 旋转速度因子
                );
            } else {
                // 如果没有移动输入，保持水平静止 (X, Z 速度为 0)，但保留 Y 轴速度
                const currentLinearVelocity = this.physicsBody.getLinearVelocity();
                this.physicsBody.setLinearVelocity(new Vector3(0, currentLinearVelocity.y, 0));
            }
        } else {
            // --- 远程玩家的更新逻辑 (占位符) ---
            // 未来这里将实现基于服务器同步数据的插值或外推，使远程玩家移动平滑
            // 例如: this.mesh.position = Vector3.Lerp(this.mesh.position, this.serverSyncedPosition, 0.1);
        }
    }

    /**
     * 为本地玩家应用移动输入。
     * @param {BABYLON.Vector3} inputVector - 输入方向向量（例如：从键盘或摇杆获取的相对世界轴的 (x,0,z) 向量）。
     */
    applyMovementInput(inputVector) {
        // 如果输入向量有显著长度，则更新移动方向和期望旋转
        if (inputVector.lengthSquared() > 0.001) {
            // 归一化输入向量以获得纯粹的方向
            this.currentMovementInput.copyFrom(inputVector.normalize());
            // 根据输入向量计算玩家Y轴的期望旋转角度
            // Math.atan2(y, x) 返回点 (x,y) 与正X轴之间的角度
            // 在 Babylon.js 中，Z轴是“前进”方向，X轴是“右侧”方向，所以用 atan2(X, Z)
            this.desiredRotationY = Math.atan2(inputVector.x, inputVector.z);
        } else {
            // 如果没有输入，清除移动输入向量
            this.currentMovementInput.copyFrom(Vector3.Zero());
        }
    }

    /**
     * 直接设置玩家的位置和旋转（通常用于服务器同步或瞬间传送）。
     * @param {BABYLON.Vector3} position - 新的位置。
     * @param {BABYLON.Quaternion} rotationQuaternion - 新的旋转四元数。
     */
    setPositionAndRotation(position, rotationQuaternion) {
        // 直接更新物理体所关联的变换节点的位置和旋转
        // 由于 mesh 就是 physicsBody 的 transformNode，所以直接设置 mesh 即可
        this.mesh.position.copyFrom(position);
        this.mesh.rotationQuaternion.copyFrom(rotationQuaternion);

        // 如果需要，也可以通过物理体来设置，但这通常用于物理引擎控制的位置/旋转
        // this.physicsBody.setTargetTransform(new BABYLON.TransformNode("target", this.scene));
        // this.physicsBody.transformNode.position.copyFrom(position);
        // this.physicsBody.transformNode.rotationQuaternion.copyFrom(rotationQuaternion);
        // 确保物理引擎在下一次迭代中能正确处理此瞬间移动
        this.physicsBody.setLinearVelocity(Vector3.Zero());
        this.physicsBody.setAngularVelocity(Vector3.Zero());
    }

    /**
     * 释放玩家实例占用的资源。
     */
    dispose() {
        if (this.physicsBody) {
            this.physicsBody.dispose(); // 释放物理体资源
            this.physicsBody = null;
        }
        if (this.mesh) {
            this.mesh.dispose(); // 释放网格资源
            this.mesh = null;
        }
        console.log(`玩家实例 '${this.id}' 已销毁。`);
    }
}
