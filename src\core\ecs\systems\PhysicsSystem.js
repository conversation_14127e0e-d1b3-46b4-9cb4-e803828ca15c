/**
 * 物理系统 (PhysicsSystem)
 * 为bitECS提供物理模拟功能，与PhysicsManager集成
 * 处理物理体的创建、更新和同步
 */

import { query } from 'bitecs';
import { Transform, Physics, Render } from '../bitECSSystem.js';
import { Vector3 } from '@babylonjs/core/Maths/math.vector';

/**
 * 物理系统主函数
 * 处理所有具有物理组件的实体
 * @param {Object} world - bitECS世界实例
 * @param {number} deltaTime - 时间增量（秒）
 */
export function physicsSystem(world, deltaTime) {
    // 查询所有具有Transform和Physics组件的实体
    const physicsEntities = query(world, [Transform, Physics]);
    
    for (const entity of physicsEntities) {
        // 获取物理组件数据
        const mass = Physics.mass[entity];
        const velocityX = Physics.velocityX[entity];
        const velocityY = Physics.velocityY[entity];
        const velocityZ = Physics.velocityZ[entity];
        const friction = Physics.friction[entity] || 0.9;
        const isStatic = Physics.isStatic[entity];
        const isKinematic = Physics.isKinematic[entity];
        
        // 跳过静态物体的物理计算
        if (isStatic) {
            continue;
        }
        
        // 应用重力（如果不是运动学物体）
        if (!isKinematic && mass > 0) {
            const gravity = -9.81; // 重力加速度
            Physics.velocityY[entity] += gravity * deltaTime;
        }
        
        // 更新位置
        Transform.x[entity] += Physics.velocityX[entity] * deltaTime;
        Transform.y[entity] += Physics.velocityY[entity] * deltaTime;
        Transform.z[entity] += Physics.velocityZ[entity] * deltaTime;
        
        // 简单的地面碰撞检测
        if (Transform.y[entity] < 0) {
            Transform.y[entity] = 0;
            Physics.velocityY[entity] = -Physics.velocityY[entity] * (Physics.restitution[entity] || 0.3);
            
            // 如果反弹速度很小，停止反弹
            if (Math.abs(Physics.velocityY[entity]) < 0.1) {
                Physics.velocityY[entity] = 0;
            }
        }
        
        // 应用摩擦力
        Physics.velocityX[entity] *= friction;
        Physics.velocityZ[entity] *= friction;
        
        // 清理微小的速度值以避免浮点精度问题
        if (Math.abs(Physics.velocityX[entity]) < 0.01) {
            Physics.velocityX[entity] = 0;
        }
        if (Math.abs(Physics.velocityZ[entity]) < 0.01) {
            Physics.velocityZ[entity] = 0;
        }
    }
}

/**
 * Havok物理同步系统
 * 将bitECS物理组件与Havok物理引擎同步
 * @param {Object} world - bitECS世界实例
 * @param {number} deltaTime - 时间增量
 * @param {PhysicsManager} physicsManager - 物理管理器实例
 */
export function havokPhysicsSyncSystem(world, deltaTime, physicsManager) {
    if (!physicsManager || !physicsManager.isReady()) {
        return;
    }
    
    // 查询需要同步的物理实体
    const syncEntities = query(world, [Transform, Physics]);
    
    for (const entity of syncEntities) {
        // 这里可以添加与Havok物理引擎的同步逻辑
        // 例如：从Havok物理体获取位置和速度，更新到ECS组件
        
        // 获取实体关联的物理体（需要在创建时建立映射关系）
        const physicsBodyId = Physics.physicsBodyId?.[entity];
        if (physicsBodyId) {
            // 从物理管理器获取物理体
            const physicsBodyData = physicsManager.physicsBodies.get(physicsBodyId);
            if (physicsBodyData && physicsBodyData.body) {
                const physicsBody = physicsBodyData.body;
                
                try {
                    // 从物理体获取位置
                    const position = physicsBody.transformNode.getAbsolutePosition();
                    Transform.x[entity] = position.x;
                    Transform.y[entity] = position.y;
                    Transform.z[entity] = position.z;
                    
                    // 从物理体获取速度
                    const velocity = physicsManager.getVelocity(physicsBody);
                    Physics.velocityX[entity] = velocity.x;
                    Physics.velocityY[entity] = velocity.y;
                    Physics.velocityZ[entity] = velocity.z;
                } catch (error) {
                    console.warn(`同步物理实体 ${entity} 失败:`, error);
                }
            }
        }
    }
}

/**
 * 物理体创建辅助函数
 * 为ECS实体创建对应的Havok物理体
 * @param {Object} ecsWorld - ECS世界实例
 * @param {PhysicsManager} physicsManager - 物理管理器
 * @param {number} entity - 实体ID
 * @param {Object} mesh - Babylon.js网格对象
 * @param {Object} options - 物理体选项
 * @returns {PhysicsBody|null} 创建的物理体
 */
export function createPhysicsBodyForEntity(ecsWorld, physicsManager, entity, mesh, options = {}) {
    if (!physicsManager || !physicsManager.isReady()) {
        console.warn('物理管理器未准备就绪，无法创建物理体');
        return null;
    }
    
    // 从ECS组件获取物理属性
    const mass = Physics.mass[entity] || 1;
    const friction = Physics.friction[entity] || 0.5;
    const restitution = Physics.restitution[entity] || 0.3;
    const isStatic = Physics.isStatic[entity] || false;
    const isKinematic = Physics.isKinematic[entity] || false;
    
    // 确定运动类型
    let motionType;
    if (isStatic) {
        motionType = 'STATIC';
    } else if (isKinematic) {
        motionType = 'KINEMATIC';
    } else {
        motionType = 'DYNAMIC';
    }
    
    // 创建物理体选项
    const physicsOptions = {
        mass: mass,
        motionType: motionType,
        material: {
            friction: friction,
            restitution: restitution
        },
        ...options
    };
    
    // 创建物理体
    const physicsBody = physicsManager.createRigidBody(mesh, physicsOptions);
    
    if (physicsBody) {
        // 建立ECS实体与物理体的映射关系
        // 注意：这需要扩展Physics组件以包含physicsBodyId字段
        const bodyId = physicsManager.generateBodyId();
        
        // 如果Physics组件有physicsBodyId字段，设置它
        if (Physics.physicsBodyId) {
            Physics.physicsBodyId[entity] = bodyId;
        }
        
        console.log(`为实体 ${entity} 创建物理体成功: ${bodyId}`);
    }
    
    return physicsBody;
}

/**
 * 应用力到ECS实体
 * @param {PhysicsManager} physicsManager - 物理管理器
 * @param {number} entity - 实体ID
 * @param {Vector3} force - 力向量
 * @param {Vector3} contactPoint - 作用点（可选）
 */
export function applyForceToEntity(physicsManager, entity, force, contactPoint = null) {
    if (!physicsManager || !Physics.physicsBodyId) {
        return;
    }
    
    const physicsBodyId = Physics.physicsBodyId[entity];
    if (physicsBodyId) {
        const physicsBodyData = physicsManager.physicsBodies.get(physicsBodyId);
        if (physicsBodyData && physicsBodyData.body) {
            physicsManager.applyForce(physicsBodyData.body, force, contactPoint);
        }
    }
}

/**
 * 应用冲量到ECS实体
 * @param {PhysicsManager} physicsManager - 物理管理器
 * @param {number} entity - 实体ID
 * @param {Vector3} impulse - 冲量向量
 * @param {Vector3} contactPoint - 作用点（可选）
 */
export function applyImpulseToEntity(physicsManager, entity, impulse, contactPoint = null) {
    if (!physicsManager || !Physics.physicsBodyId) {
        return;
    }
    
    const physicsBodyId = Physics.physicsBodyId[entity];
    if (physicsBodyId) {
        const physicsBodyData = physicsManager.physicsBodies.get(physicsBodyId);
        if (physicsBodyData && physicsBodyData.body) {
            physicsManager.applyImpulse(physicsBodyData.body, impulse, contactPoint);
        }
    }
}

console.log('PhysicsSystem模块已加载');
