// src/core/assets/LoadingProgressManager.unit.test.js
// 加载进度管理器单元测试

import { jest } from '@jest/globals';
import LoadingProgressManager, { ProgressStatus } from './LoadingProgressManager.js';

describe('LoadingProgressManager', () => {
    let progressManager;

    beforeEach(() => {
        progressManager = new LoadingProgressManager();
    });

    afterEach(() => {
        if (progressManager) {
            progressManager.dispose();
        }
    });

    describe('单个进度管理', () => {
        test('应该能够创建进度跟踪', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture', 1024);
            
            expect(progressId).toBeDefined();
            expect(progressId).toMatch(/^progress_\d+$/);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress).toBeDefined();
            expect(progress.url).toBe('test.jpg');
            expect(progress.type).toBe('texture');
            expect(progress.status).toBe(ProgressStatus.PENDING);
            expect(progress.progress).toBe(0);
        });

        test('应该能够更新进度', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            
            progressManager.updateProgress(progressId, 50, 512);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress.status).toBe(ProgressStatus.LOADING);
            expect(progress.progress).toBe(50);
            expect(progress.loadedSize).toBe(512);
        });

        test('应该能够完成进度', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            
            progressManager.updateProgress(progressId, 50);
            progressManager.completeProgress(progressId);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress.status).toBe(ProgressStatus.COMPLETED);
            expect(progress.progress).toBe(100);
            expect(progress.loadTime).toBeGreaterThan(0);
        });

        test('应该能够标记进度失败', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            const error = new Error('加载失败');
            
            progressManager.failProgress(progressId, error);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress.status).toBe(ProgressStatus.FAILED);
            expect(progress.error).toBe('加载失败');
        });

        test('应该能够取消进度', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            
            progressManager.cancelProgress(progressId);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress.status).toBe(ProgressStatus.CANCELLED);
        });

        test('应该正确计算加载速度和剩余时间', () => {
            const originalNow = performance.now;
            let currentTime = 1000;
            performance.now = jest.fn(() => currentTime);
            
            const progressId = progressManager.createProgress('test.jpg', 'texture', 1000);
            
            // 开始加载
            progressManager.updateProgress(progressId, 25, 250);
            
            // 前进时间
            currentTime += 1000; // 1秒后
            
            // 更新进度
            progressManager.updateProgress(progressId, 50, 500);
            
            const progress = progressManager.getProgress(progressId);
            expect(progress.loadSpeed).toBeGreaterThan(0);
            expect(progress.estimatedTimeRemaining).toBeGreaterThan(0);
            
            performance.now = originalNow;
        });
    });

    describe('批量进度管理', () => {
        test('应该能够创建批量进度跟踪', () => {
            const items = [
                { url: 'item1.jpg', type: 'texture' },
                { url: 'item2.jpg', type: 'texture' },
                { url: 'item3.jpg', type: 'texture' }
            ];
            
            const batchId = progressManager.createBatchProgress(items);
            
            expect(batchId).toBeDefined();
            expect(batchId).toMatch(/^batch_\d+$/);
            
            const batchProgress = progressManager.getBatchProgress(batchId);
            expect(batchProgress).toBeDefined();
            expect(batchProgress.totalCount).toBe(3);
            expect(batchProgress.completedCount).toBe(0);
            expect(batchProgress.status).toBe(ProgressStatus.PENDING);
        });

        test('应该能够更新批量进度', () => {
            const items = [
                { url: 'item1.jpg', type: 'texture' },
                { url: 'item2.jpg', type: 'texture' }
            ];
            
            const batchId = progressManager.createBatchProgress(items);
            
            progressManager.updateBatchProgress(batchId, 75);
            
            const batchProgress = progressManager.getBatchProgress(batchId);
            expect(batchProgress.status).toBe(ProgressStatus.LOADING);
            expect(batchProgress.progress).toBe(75);
        });

        test('应该能够更新单个项目进度', () => {
            const items = [
                { url: 'item1.jpg', type: 'texture' },
                { url: 'item2.jpg', type: 'texture' }
            ];
            
            const batchId = progressManager.createBatchProgress(items);
            
            progressManager.updateBatchItemProgress(batchId, 'item1', 100);
            progressManager.updateBatchItemProgress(batchId, 'item2', 50);
            
            const batchProgress = progressManager.getBatchProgress(batchId);
            expect(batchProgress.progress).toBe(75); // (100 + 50) / 2
            expect(batchProgress.individualProgress.item1).toBe(100);
            expect(batchProgress.individualProgress.item2).toBe(50);
        });

        test('应该能够完成批量进度', () => {
            const items = [{ url: 'item1.jpg', type: 'texture' }];
            
            const batchId = progressManager.createBatchProgress(items);
            
            progressManager.completeBatchProgress(batchId);
            
            const batchProgress = progressManager.getBatchProgress(batchId);
            expect(batchProgress.status).toBe(ProgressStatus.COMPLETED);
            expect(batchProgress.progress).toBe(100);
        });

        test('应该能够标记批量进度失败', () => {
            const items = [{ url: 'item1.jpg', type: 'texture' }];
            const error = new Error('批量加载失败');
            
            const batchId = progressManager.createBatchProgress(items);
            
            progressManager.failBatchProgress(batchId, error);
            
            const batchProgress = progressManager.getBatchProgress(batchId);
            expect(batchProgress.status).toBe(ProgressStatus.FAILED);
        });
    });

    describe('整体进度信息', () => {
        test('应该提供整体进度概览', () => {
            // 创建一些进度项
            const progressId1 = progressManager.createProgress('test1.jpg', 'texture');
            const progressId2 = progressManager.createProgress('test2.jpg', 'texture');
            
            progressManager.updateProgress(progressId1, 50);
            progressManager.updateProgress(progressId2, 75);
            
            const overallProgress = progressManager.getOverallProgress();
            
            expect(overallProgress.activeItems).toBe(2);
            expect(overallProgress.isLoading).toBe(true);
            expect(parseFloat(overallProgress.totalProgress)).toBe(62.5); // (50 + 75) / 2
        });

        test('应该提供所有进度信息', () => {
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            const batchId = progressManager.createBatchProgress([
                { url: 'batch1.jpg', type: 'texture' }
            ]);
            
            const allProgress = progressManager.getAllProgress();
            
            expect(allProgress.individual).toHaveProperty(progressId);
            expect(allProgress.batch).toHaveProperty(batchId);
        });
    });

    describe('统计信息', () => {
        test('应该提供统计信息', () => {
            progressManager.createProgress('test1.jpg', 'texture');
            progressManager.createProgress('test2.jpg', 'texture');
            
            const stats = progressManager.getStats();
            
            expect(stats.totalCreated).toBe(2);
            expect(stats.activeItems).toBe(2);
            expect(stats.totalCompleted).toBe(0);
            expect(stats.totalFailed).toBe(0);
        });

        test('应该正确更新统计信息', () => {
            const progressId1 = progressManager.createProgress('test1.jpg', 'texture');
            const progressId2 = progressManager.createProgress('test2.jpg', 'texture');
            
            progressManager.completeProgress(progressId1);
            progressManager.failProgress(progressId2, new Error('失败'));
            
            const stats = progressManager.getStats();
            
            expect(stats.totalCompleted).toBe(1);
            expect(stats.totalFailed).toBe(1);
        });
    });

    describe('清理功能', () => {
        test('应该能够清理过期的进度项', () => {
            const originalNow = Date.now;
            let currentTime = 1000000;
            Date.now = jest.fn(() => currentTime);
            
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            progressManager.completeProgress(progressId);
            
            // 前进时间
            currentTime += 6 * 60 * 1000; // 6分钟后
            
            progressManager.cleanup(5 * 60 * 1000); // 清理5分钟前的项目
            
            const progress = progressManager.getProgress(progressId);
            expect(progress).toBeNull();
            
            Date.now = originalNow;
        });

        test('应该保留未过期的进度项', () => {
            const originalNow = Date.now;
            let currentTime = 1000000;
            Date.now = jest.fn(() => currentTime);
            
            const progressId = progressManager.createProgress('test.jpg', 'texture');
            progressManager.completeProgress(progressId);
            
            // 前进时间
            currentTime += 3 * 60 * 1000; // 3分钟后
            
            progressManager.cleanup(5 * 60 * 1000); // 清理5分钟前的项目
            
            const progress = progressManager.getProgress(progressId);
            expect(progress).toBeDefined();
            
            Date.now = originalNow;
        });
    });

    describe('错误处理', () => {
        test('应该处理不存在的进度ID', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            progressManager.updateProgress('non-existent', 50);
            progressManager.completeProgress('non-existent');
            progressManager.failProgress('non-existent', new Error('test'));
            progressManager.cancelProgress('non-existent');
            
            expect(consoleSpy).toHaveBeenCalledTimes(4);
            
            consoleSpy.mockRestore();
        });

        test('应该处理不存在的批量进度ID', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            progressManager.updateBatchProgress('non-existent', 50);
            progressManager.updateBatchItemProgress('non-existent', 'item', 50);
            progressManager.completeBatchProgress('non-existent');
            progressManager.failBatchProgress('non-existent', new Error('test'));
            
            expect(consoleSpy).toHaveBeenCalledTimes(4);
            
            consoleSpy.mockRestore();
        });

        test('应该返回null对于不存在的进度', () => {
            const progress = progressManager.getProgress('non-existent');
            const batchProgress = progressManager.getBatchProgress('non-existent');
            
            expect(progress).toBeNull();
            expect(batchProgress).toBeNull();
        });
    });

    describe('清理', () => {
        test('应该正确清理所有数据', () => {
            progressManager.createProgress('test1.jpg', 'texture');
            progressManager.createBatchProgress([{ url: 'test2.jpg', type: 'texture' }]);
            
            expect(progressManager.getStats().activeItems).toBeGreaterThan(0);
            
            progressManager.dispose();
            
            expect(progressManager.progressItems.size).toBe(0);
            expect(progressManager.batchProgressItems.size).toBe(0);
        });
    });
});
