{"compilerOptions": {"module": "NodeNext", "target": "ESNext", "moduleResolution": "NodeNext", "lib": ["ESNext", "DOM"], "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "esModuleInterop": true, "noImplicitAny": false, "noLib": false, "removeComments": true, "sourceMap": true, "rootDir": "src", "outDir": "dist", "baseUrl": ".", "paths": {"bitecs": ["src/core/index.ts"], "bitecs/*": ["src/*/index.ts"]}}, "include": ["./src/core", "./src/serialization"], "exclude": ["node_modules", "test/*"]}