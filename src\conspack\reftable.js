/*
(defun list-length-with-refs (list &optional (context *ref-context*))
  (if context
      (loop for i on list
            summing 1 into count
            until (and (consp (cdr i))
                       (get-ref-id (cdr i) context))
            finally (return (1+ count)))
      (loop for i on list
            summing 1 into count
            while (consp (cdr i))
            finally (return (1+ count)))))
*/

/**
 *
 * @param {any[]} vec
 * @returns {int}
 */
function list_length_with_refs(vec) {
    return 1 + vec.length;
}

export default list_length_with_refs;
