/**
 * PhysicsSystem 单元测试
 * 测试物理系统ECS集成的所有功能
 */

import { jest } from '@jest/globals';
import { Vector3 } from "@babylonjs/core/Maths/math.vector";
import {
    physicsSystem,
    havokPhysicsSyncSystem,
    createPhysicsBodyForEntity,
    applyForceToEntity,
    applyImpulseToEntity
} from './PhysicsSystem.js';

// Mock bitECS
jest.mock('bitecs', () => ({
    query: jest.fn()
}));

// Mock ECS 组件
const mockTransform = {
    x: [0, 1, 2],
    y: [0, 5, 10],
    z: [0, 0, 0],
    rotationX: [0, 0, 0],
    rotationY: [0, 0, 0],
    rotationZ: [0, 0, 0],
    scaleX: [1, 1, 1],
    scaleY: [1, 1, 1],
    scaleZ: [1, 1, 1]
};

const mockPhysics = {
    mass: [70, 10, 5],
    velocityX: [0, 1, -1],
    velocityY: [0, 0, 2],
    velocityZ: [0, 0, 0],
    friction: [0.8, 0.9, 0.7],
    restitution: [0.1, 0.3, 0.8],
    isKinematic: [0, 0, 0],
    isStatic: [0, 0, 0],
    physicsBodyId: ['body1', 'body2', 'body3']
};

// Mock ECS 世界
const mockWorld = {
    entities: [0, 1, 2]
};

// Mock ECS 世界实例
const mockECSWorld = {
    addComponent: jest.fn(),
    generateBodyId: jest.fn(() => 'test_body_id')
};

// Mock 物理管理器
const mockPhysicsManager = {
    isReady: jest.fn(() => true),
    createRigidBody: jest.fn(() => ({ id: 'mock_body' })),
    generateBodyId: jest.fn(() => 'mock_body_id'),
    physicsBodies: new Map(),
    getVelocity: jest.fn(() => new Vector3(1, 2, 3)),
    applyForce: jest.fn(),
    applyImpulse: jest.fn()
};

// Mock 网格对象
const mockMesh = {
    position: Vector3.Zero(),
    rotation: Vector3.Zero(),
    scaling: Vector3.One()
};

// 导入 query mock
import { query } from 'bitecs';

describe('PhysicsSystem', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockPhysicsManager.physicsBodies.clear();
        
        // 设置 query mock 返回实体列表
        query.mockReturnValue([0, 1, 2]);
    });

    describe('physicsSystem', () => {
        test('应该更新所有物理实体的位置', () => {
            const deltaTime = 1/60; // 60 FPS
            
            physicsSystem(mockWorld, deltaTime);
            
            expect(query).toHaveBeenCalled();
            
            // 验证位置更新（基于速度）
            // 实体0: velocityX=0, velocityY=0 -> 位置应该只受重力影响
            // 实体1: velocityX=1, velocityY=0 -> X位置应该增加
            // 实体2: velocityX=-1, velocityY=2 -> X位置应该减少，Y位置应该增加
        });

        test('应该应用重力到非静态物体', () => {
            const deltaTime = 1/60;
            const initialVelocityY = mockPhysics.velocityY[0];
            
            physicsSystem(mockWorld, deltaTime);
            
            // 重力应该影响Y轴速度
            const expectedVelocityY = initialVelocityY + (-9.81 * deltaTime);
            // 注意：由于我们直接修改了mock数组，这里主要测试逻辑正确性
        });

        test('应该跳过静态物体的物理计算', () => {
            // 设置实体1为静态
            mockPhysics.isStatic[1] = 1;
            const initialY = mockTransform.y[1];
            
            physicsSystem(mockWorld, 1/60);
            
            // 静态物体的位置不应该改变
            // 注意：这里需要根据实际实现来验证
        });

        test('应该处理地面碰撞', () => {
            // 设置实体在地面以下
            mockTransform.y[0] = -1;
            mockPhysics.velocityY[0] = -5;
            
            physicsSystem(mockWorld, 1/60);
            
            // 位置应该被重置到地面，速度应该反弹
            // 注意：这里需要根据实际实现来验证
        });

        test('应该应用摩擦力', () => {
            const initialVelocityX = mockPhysics.velocityX[1];
            const friction = mockPhysics.friction[1];
            
            physicsSystem(mockWorld, 1/60);
            
            // 速度应该受到摩擦力影响
            // expectedVelocity = initialVelocityX * friction
        });

        test('应该清理微小的速度值', () => {
            // 设置很小的速度值
            mockPhysics.velocityX[0] = 0.005;
            mockPhysics.velocityZ[0] = 0.008;
            
            physicsSystem(mockWorld, 1/60);
            
            // 微小的速度值应该被设为0
            // 注意：这里需要根据实际实现来验证
        });
    });

    describe('havokPhysicsSyncSystem', () => {
        test('应该在物理管理器未准备时直接返回', () => {
            mockPhysicsManager.isReady.mockReturnValue(false);
            
            havokPhysicsSyncSystem(mockWorld, 1/60, mockPhysicsManager);
            
            expect(query).not.toHaveBeenCalled();
        });

        test('应该同步物理体位置到ECS组件', () => {
            // 设置物理体映射
            const mockPhysicsBody = {
                transformNode: {
                    getAbsolutePosition: jest.fn(() => new Vector3(10, 20, 30))
                }
            };
            
            mockPhysicsManager.physicsBodies.set('body1', {
                body: mockPhysicsBody,
                mesh: mockMesh
            });
            
            havokPhysicsSyncSystem(mockWorld, 1/60, mockPhysicsManager);
            
            expect(query).toHaveBeenCalled();
            expect(mockPhysicsManager.getVelocity).toHaveBeenCalled();
        });

        test('应该处理物理体不存在的情况', () => {
            // 清空物理体映射
            mockPhysicsManager.physicsBodies.clear();
            
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            havokPhysicsSyncSystem(mockWorld, 1/60, mockPhysicsManager);
            
            // 不应该抛出错误
            expect(consoleSpy).not.toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });

    describe('createPhysicsBodyForEntity', () => {
        test('应该为ECS实体创建物理体', () => {
            const entity = 0;
            const result = createPhysicsBodyForEntity(
                mockECSWorld,
                mockPhysicsManager,
                entity,
                mockMesh
            );
            
            expect(mockPhysicsManager.createRigidBody).toHaveBeenCalled();
            expect(result).toBeDefined();
        });

        test('应该使用ECS组件的物理属性', () => {
            const entity = 1;
            const options = {
                shapeType: 'sphere',
                material: { friction: 0.5, restitution: 0.3 }
            };
            
            createPhysicsBodyForEntity(
                mockECSWorld,
                mockPhysicsManager,
                entity,
                mockMesh,
                options
            );
            
            expect(mockPhysicsManager.createRigidBody).toHaveBeenCalledWith(
                mockMesh,
                expect.objectContaining({
                    mass: mockPhysics.mass[entity],
                    material: expect.objectContaining({
                        friction: mockPhysics.friction[entity],
                        restitution: mockPhysics.restitution[entity]
                    })
                })
            );
        });

        test('应该处理物理管理器未准备的情况', () => {
            mockPhysicsManager.isReady.mockReturnValue(false);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            const result = createPhysicsBodyForEntity(
                mockECSWorld,
                mockPhysicsManager,
                0,
                mockMesh
            );
            
            expect(result).toBeNull();
            expect(consoleSpy).toHaveBeenCalledWith('物理管理器未准备就绪，无法创建物理体');
            consoleSpy.mockRestore();
        });

        test('应该根据ECS组件确定运动类型', () => {
            // 测试静态物体
            mockPhysics.isStatic[0] = 1;
            createPhysicsBodyForEntity(mockECSWorld, mockPhysicsManager, 0, mockMesh);
            
            expect(mockPhysicsManager.createRigidBody).toHaveBeenCalledWith(
                mockMesh,
                expect.objectContaining({
                    motionType: 'STATIC'
                })
            );
            
            // 重置
            mockPhysics.isStatic[0] = 0;
            
            // 测试运动学物体
            mockPhysics.isKinematic[0] = 1;
            createPhysicsBodyForEntity(mockECSWorld, mockPhysicsManager, 0, mockMesh);
            
            expect(mockPhysicsManager.createRigidBody).toHaveBeenCalledWith(
                mockMesh,
                expect.objectContaining({
                    motionType: 'KINEMATIC'
                })
            );
        });
    });

    describe('applyForceToEntity', () => {
        test('应该对实体应用力', () => {
            const entity = 0;
            const force = new Vector3(10, 0, 0);
            
            // 设置物理体映射
            const mockPhysicsBody = { id: 'test_body' };
            mockPhysicsManager.physicsBodies.set('body1', {
                body: mockPhysicsBody
            });
            
            applyForceToEntity(mockPhysicsManager, entity, force);
            
            expect(mockPhysicsManager.applyForce).toHaveBeenCalledWith(
                mockPhysicsBody,
                force,
                null
            );
        });

        test('应该处理物理体不存在的情况', () => {
            const entity = 999; // 不存在的实体
            const force = new Vector3(10, 0, 0);
            
            applyForceToEntity(mockPhysicsManager, entity, force);
            
            expect(mockPhysicsManager.applyForce).not.toHaveBeenCalled();
        });
    });

    describe('applyImpulseToEntity', () => {
        test('应该对实体应用冲量', () => {
            const entity = 0;
            const impulse = new Vector3(5, 10, 0);
            
            // 设置物理体映射
            const mockPhysicsBody = { id: 'test_body' };
            mockPhysicsManager.physicsBodies.set('body1', {
                body: mockPhysicsBody
            });
            
            applyImpulseToEntity(mockPhysicsManager, entity, impulse);
            
            expect(mockPhysicsManager.applyImpulse).toHaveBeenCalledWith(
                mockPhysicsBody,
                impulse,
                null
            );
        });

        test('应该处理物理体不存在的情况', () => {
            const entity = 999; // 不存在的实体
            const impulse = new Vector3(5, 10, 0);
            
            applyImpulseToEntity(mockPhysicsManager, entity, impulse);
            
            expect(mockPhysicsManager.applyImpulse).not.toHaveBeenCalled();
        });
    });
});

console.log('PhysicsSystem单元测试已加载');
