## 使用示例

以下是如何使用 EventEmitter 的示例代码，展示其主要功能：

```js
// 创建 EventEmitter 实例
const emitter = new EventEmitter();

// 设置命名空间为 'user'
emitter.namespace('user');

// 注册事件监听器
emitter.on('login', (name) => {
  console.log(`用户登录: ${name}`);
});

// 触发事件
emitter.emit('login', 'Alice'); // 输出: 用户登录: Alice

// 注册一次性监听器
emitter.once('logout', (name) => {
  console.log(`用户登出: ${name}`);
});

// 触发事件
emitter.emit('logout', 'Bob'); // 输出: 用户登出: Bob
emitter.emit('logout', 'Charlie'); // 无输出，因为 once 监听器已移除

// 切换命名空间到 'game'
emitter.namespace('game');

// 注册游戏事件
emitter.on('start', () => {
  console.log('游戏开始');
});

// 触发游戏事件
emitter.emit('start'); // 输出: 游戏开始

// 移除监听器
const callback = () => console.log('数据事件');
emitter.on('data', callback);
emitter.emit('data'); // 输出: 数据事件
emitter.off('data', callback);
emitter.emit('data'); // 无输出

// 移除所有监听器
emitter.on('event', () => console.log('事件触发'));
emitter.removeAllListeners('event');
emitter.emit('event'); // 无输出
```



## 项目文档

### 概述

EventEmitter 是一个纯 JavaScript 实现的事件发射器，专为浏览器环境设计，支持客户端事件驱动编程。它提供命名空间功能，用于组织事件并避免冲突。代码简洁，易于集成，且无需外部依赖。

### 功能

以下是 EventEmitter 提供的主要方法及其描述：

- namespace(namespace)
  - 描述: 设置当前命名空间。
  - 参数:
    - namespace (string): 命名空间。
  - 返回: EventEmitter 实例，支持链式调用。
  - 示例: emitter.namespace('user').on('login', callback);
- on(eventName, callback)
  - 描述: 注册一个事件监听器。
  - 参数:
    - eventName (string): 事件名称。
    - callback (Function): 监听器函数。
  - 示例: emitter.on('click', () => console.log('点击事件'));
- once(eventName, callback)
  - 描述: 注册一个一次性监听器，触发一次后自动移除。
  - 参数:
    - eventName (string): 事件名称。
    - callback (Function): 监听器函数。
  - 示例: emitter.once('init', () => console.log('初始化'));
- emit(eventName, ...args)
  - 描述: 触发指定事件，调用所有相关监听器，并传递参数。
  - 参数:
    - eventName (string): 事件名称。
    - ...args (any): 传递给监听器的参数。
  - 示例: emitter.emit('update', '新数据');
- off(eventName, callback)
  - 描述: 移除指定事件的特定监听器。
  - 参数:
    - eventName (string): 事件名称。
    - callback (Function): 要移除的监听器函数。
  - 示例: emitter.off('click', handler);
- removeAllListeners(eventName)
  - 描述: 移除指定事件的所有监听器。
  - 参数:
    - eventName (string): 事件名称。
  - 示例: emitter.removeAllListeners('click');
- listeners(eventName)
  - 描述: 获取指定事件的所有监听器。
  - 参数:
    - eventName (string): 事件名称。
  - 返回: Function[] - 监听器数组。
  - 示例: const handlers = emitter.listeners('click');

### 错误处理

- 在 emit 方法中，监听器执行时发生的错误会被捕获并通过 console.error 输出，确保一个监听器的错误不会影响其他监听器的执行。

### 兼容性

- 环境: 兼容所有支持 ES5 的浏览器（如 IE9+）。
- 依赖: 无需外部库或打包工具，直接在浏览器中使用。

### 如何使用

1. 保存代码: 将上述源代码保存为 eventEmitter.js。

2. 引入文件: 在 HTML 中通过 <script> 标签引入：

   html

   ```html
   <script src="eventEmitter.js"></script>
   ```

3. 创建实例: 在 JavaScript 中使用：

   javascript

   ```javascript
   const emitter = new window.EventEmitter();
   emitter.namespace('test').on('event', (data) => console.log('测试事件:', data));
   emitter.emit('event', 'Hello'); // 输出: 测试事件: Hello
   ```

### 注意事项

- 事件名敏感: 事件名区分大小写，例如 'click' 和 'Click' 是不同的事件。
- 命名空间: 事件名会自动添加当前命名空间前缀，确保事件名的唯一性。
- 执行顺序: 监听器按注册顺序依次执行。
- 未注册事件: 触发未注册的事件不会抛出错误，仅无声忽略。

## 总结

这个 EventEmitter 实现提供了事件驱动编程的核心功能，支持命名空间，适用于浏览器环境。它代码简洁、注释详尽、文档清晰，并具备错误处理机制，可轻松集成到您的项目中，满足大量事件驱动编程的需求。