{"name": "cantos-client", "version": "0.1.0", "description": "Cantos client.", "main": "src/index.js", "dependencies": {"@babylonjs/assets": "^5.20.0", "@babylonjs/core": "^8.12.1", "@babylonjs/gui-editor": "^8.12.1", "@babylonjs/havok": "^1.3.10", "@babylonjs/inspector": "^8.12.1", "@babylonjs/loaders": "^8.12.1", "@babylonjs/materials": "^8.12.1", "ammo.js": "github:kripken/ammo.js", "bitecs": "file:./libs/bitECS", "recast-detour": "^1.6.4"}, "devDependencies": {"@playwright/test": "^1.53.0", "@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@eslint/js": "^9.28.0", "babel-jest": "^29.7.0", "babel-loader": "^10.0.0", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "eslint": "^9.28.0", "file-loader": "^6.2.0", "globals": "^16.2.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "source-map-loader": "^5.0.0", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1"}, "scripts": {"start": "npx webpack serve --config webpack.dev.cjs", "start:test": "npx webpack serve --config webpack.tests.cjs", "build:dev": "npx webpack --config webpack.dev.cjs", "build": "npx webpack --config webpack.prod.cjs", "lint": "npx eslint . --ext .js,.jsx", "test:visuals": "npx playwright test", "test:unit": "cross-env NODE_OPTIONS=--experimental-vm-modules npx jest"}, "repository": {"type": "git", "url": "https://gitee.com/hxz/cantos-client"}, "keywords": ["Babylon.js", "webpack", "javascript", "es6"], "author": {"name": "<PERSON>zhi", "email": "<EMAIL>"}, "license": "Private", "homepage": "https://gitee.com/hxz/cantos-client"}