// src/config/terrain.js
// 地形生成配置文件

/**
 * 地形生成配置
 * 定义了不同类型地形的生成参数和材质配置
 */
export const TerrainConfig = {
    // 默认地形配置
    default: {
        // 地形尺寸设置
        size: { 
            width: 512,     // 地形宽度（世界单位）
            height: 512     // 地形深度（世界单位）
        },
        subdivisions: 128,  // 地形细分数，影响地形精度和性能
        
        // 高度范围设置
        heightRange: { 
            min: 0,         // 最低高度
            max: 50         // 最高高度
        },
        
        // 柏林噪声参数
        noiseSettings: {
            octaves: 4,         // 噪声层数，影响地形细节丰富度
            persistence: 0.5,   // 持续性，控制高频噪声的影响
            lacunarity: 2.0,    // 间隙性，控制噪声频率的递增
            scale: 0.01,        // 噪声缩放，影响地形的整体起伏
            seed: 12345         // 随机种子
        },
        
        // 材质纹理配置
        materials: {
            grass: {
                diffuse: "assets/textures/grass_diffuse.jpg",
                normal: "assets/textures/grass_normal.jpg",
                heightRange: [0, 15]  // 草地高度范围
            },
            rock: {
                diffuse: "assets/textures/rock_diffuse.jpg", 
                normal: "assets/textures/rock_normal.jpg",
                heightRange: [10, 35] // 岩石高度范围
            },
            snow: {
                diffuse: "assets/textures/snow_diffuse.jpg",
                normal: "assets/textures/snow_normal.jpg", 
                heightRange: [30, 50] // 雪地高度范围
            }
        },
        
        // 物理属性
        physics: {
            friction: 0.8,      // 摩擦力
            restitution: 0.1    // 弹性
        }
    },
    
    // 山海经主题地形配置
    shanhaijing: {
        // 东山经 - 朝阳之地
        eastMountains: {
            size: { width: 1024, height: 1024 },
            subdivisions: 256,
            heightRange: { min: 0, max: 80 },
            noiseSettings: {
                octaves: 6,
                persistence: 0.6,
                lacunarity: 2.2,
                scale: 0.008,
                seed: 11111
            },
            materials: {
                grass: {
                    diffuse: "assets/textures/ancient_grass.jpg",
                    normal: "assets/textures/ancient_grass_normal.jpg",
                    heightRange: [0, 20]
                },
                mysticalRock: {
                    diffuse: "assets/textures/mystical_rock.jpg",
                    normal: "assets/textures/mystical_rock_normal.jpg",
                    heightRange: [15, 60]
                },
                celestialPeak: {
                    diffuse: "assets/textures/celestial_peak.jpg",
                    normal: "assets/textures/celestial_peak_normal.jpg",
                    heightRange: [50, 80]
                }
            }
        },
        
        // 南山经 - 炎热之境
        southMountains: {
            size: { width: 1024, height: 1024 },
            subdivisions: 256,
            heightRange: { min: 0, max: 100 },
            noiseSettings: {
                octaves: 5,
                persistence: 0.7,
                lacunarity: 2.5,
                scale: 0.006,
                seed: 22222
            },
            materials: {
                desert: {
                    diffuse: "assets/textures/desert_sand.jpg",
                    normal: "assets/textures/desert_sand_normal.jpg",
                    heightRange: [0, 25]
                },
                volcano: {
                    diffuse: "assets/textures/volcanic_rock.jpg",
                    normal: "assets/textures/volcanic_rock_normal.jpg",
                    heightRange: [20, 70]
                },
                lava: {
                    diffuse: "assets/textures/lava_rock.jpg",
                    normal: "assets/textures/lava_rock_normal.jpg",
                    heightRange: [60, 100]
                }
            }
        },
        
        // 西山经 - 金石之域
        westMountains: {
            size: { width: 1024, height: 1024 },
            subdivisions: 256,
            heightRange: { min: 0, max: 120 },
            noiseSettings: {
                octaves: 4,
                persistence: 0.4,
                lacunarity: 2.8,
                scale: 0.005,
                seed: 33333
            },
            materials: {
                plateau: {
                    diffuse: "assets/textures/plateau_stone.jpg",
                    normal: "assets/textures/plateau_stone_normal.jpg",
                    heightRange: [0, 40]
                },
                goldRock: {
                    diffuse: "assets/textures/gold_rock.jpg",
                    normal: "assets/textures/gold_rock_normal.jpg",
                    heightRange: [30, 80]
                },
                snowPeak: {
                    diffuse: "assets/textures/snow_peak.jpg",
                    normal: "assets/textures/snow_peak_normal.jpg",
                    heightRange: [70, 120]
                }
            }
        },
        
        // 北山经 - 玄冰之野
        northMountains: {
            size: { width: 1024, height: 1024 },
            subdivisions: 256,
            heightRange: { min: 0, max: 90 },
            noiseSettings: {
                octaves: 3,
                persistence: 0.3,
                lacunarity: 3.0,
                scale: 0.007,
                seed: 44444
            },
            materials: {
                tundra: {
                    diffuse: "assets/textures/tundra.jpg",
                    normal: "assets/textures/tundra_normal.jpg",
                    heightRange: [0, 30]
                },
                ice: {
                    diffuse: "assets/textures/ice.jpg",
                    normal: "assets/textures/ice_normal.jpg",
                    heightRange: [25, 60]
                },
                glacier: {
                    diffuse: "assets/textures/glacier.jpg",
                    normal: "assets/textures/glacier_normal.jpg",
                    heightRange: [50, 90]
                }
            }
        },
        
        // 中山经 - 神州中心
        centralMountains: {
            size: { width: 2048, height: 2048 },
            subdivisions: 512,
            heightRange: { min: 0, max: 60 },
            noiseSettings: {
                octaves: 5,
                persistence: 0.5,
                lacunarity: 2.0,
                scale: 0.004,
                seed: 55555
            },
            materials: {
                fertile: {
                    diffuse: "assets/textures/fertile_soil.jpg",
                    normal: "assets/textures/fertile_soil_normal.jpg",
                    heightRange: [0, 20]
                },
                forest: {
                    diffuse: "assets/textures/forest_floor.jpg",
                    normal: "assets/textures/forest_floor_normal.jpg",
                    heightRange: [15, 40]
                },
                sacred: {
                    diffuse: "assets/textures/sacred_stone.jpg",
                    normal: "assets/textures/sacred_stone_normal.jpg",
                    heightRange: [35, 60]
                }
            }
        }
    }
};

/**
 * 地形LOD配置
 * 定义不同距离下的地形细节级别
 */
export const TerrainLODConfig = {
    levels: [
        { distance: 0, subdivisions: 256 },    // 最高细节
        { distance: 200, subdivisions: 128 },  // 中等细节
        { distance: 500, subdivisions: 64 },   // 低细节
        { distance: 1000, subdivisions: 32 }   // 最低细节
    ]
};

/**
 * 地形生成预设
 * 快速生成不同风格的地形
 */
export const TerrainPresets = {
    // 平原地形
    plains: {
        noiseSettings: {
            octaves: 2,
            persistence: 0.3,
            lacunarity: 2.0,
            scale: 0.02
        },
        heightRange: { min: 0, max: 10 }
    },
    
    // 丘陵地形
    hills: {
        noiseSettings: {
            octaves: 3,
            persistence: 0.4,
            lacunarity: 2.2,
            scale: 0.015
        },
        heightRange: { min: 0, max: 25 }
    },
    
    // 山脉地形
    mountains: {
        noiseSettings: {
            octaves: 6,
            persistence: 0.6,
            lacunarity: 2.5,
            scale: 0.008
        },
        heightRange: { min: 0, max: 80 }
    },
    
    // 高原地形
    plateau: {
        noiseSettings: {
            octaves: 4,
            persistence: 0.2,
            lacunarity: 3.0,
            scale: 0.01
        },
        heightRange: { min: 20, max: 40 }
    }
};

export default TerrainConfig;
