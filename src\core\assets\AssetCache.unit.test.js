// src/core/assets/AssetCache.unit.test.js
// 资源缓存系统单元测试

import { jest } from '@jest/globals';
import AssetCache from './AssetCache.js';

describe('AssetCache', () => {
    let cache;

    beforeEach(() => {
        cache = new AssetCache(1024); // 1KB用于测试
    });

    afterEach(() => {
        if (cache) {
            cache.clear();
        }
    });

    describe('基本缓存操作', () => {
        test('应该能够设置和获取缓存项', () => {
            const testData = { test: 'data' };
            
            cache.set('test-key', testData, 100);
            const result = cache.get('test-key');
            
            expect(result).toEqual(testData);
            expect(cache.has('test-key')).toBe(true);
        });

        test('应该返回undefined对于不存在的键', () => {
            const result = cache.get('non-existent-key');
            expect(result).toBeUndefined();
            expect(cache.has('non-existent-key')).toBe(false);
        });

        test('应该能够删除缓存项', () => {
            cache.set('delete-test', { data: true }, 50);
            
            expect(cache.has('delete-test')).toBe(true);
            
            const deleted = cache.delete('delete-test');
            
            expect(deleted).toBe(true);
            expect(cache.has('delete-test')).toBe(false);
            expect(cache.get('delete-test')).toBeUndefined();
        });

        test('删除不存在的键应该返回false', () => {
            const deleted = cache.delete('non-existent');
            expect(deleted).toBe(false);
        });
    });

    describe('LRU功能', () => {
        test('应该更新访问顺序', () => {
            cache.set('item1', 'data1', 100);
            cache.set('item2', 'data2', 100);
            cache.set('item3', 'data3', 100);
            
            // 访问item1，使其成为最近使用的
            cache.get('item1');
            
            // 添加新项目，应该驱逐item2（最少使用的）
            cache.set('item4', 'data4', 900); // 填满缓存
            
            expect(cache.has('item1')).toBe(true); // 最近访问的应该保留
            expect(cache.has('item4')).toBe(true); // 新添加的应该存在
            expect(cache.has('item2')).toBe(false); // 应该被驱逐
        });

        test('应该在缓存满时驱逐最少使用的项目', () => {
            // 填满缓存
            cache.set('item1', 'data1', 300);
            cache.set('item2', 'data2', 300);
            cache.set('item3', 'data3', 300);
            
            // 访问item1和item2
            cache.get('item1');
            cache.get('item2');
            
            // 添加新项目，应该驱逐item3
            cache.set('item4', 'data4', 200);
            
            expect(cache.has('item1')).toBe(true);
            expect(cache.has('item2')).toBe(true);
            expect(cache.has('item4')).toBe(true);
            expect(cache.has('item3')).toBe(false); // 应该被驱逐
        });
    });

    describe('大小管理', () => {
        test('应该正确跟踪缓存大小', () => {
            expect(cache.getCurrentSize()).toBe(0);
            
            cache.set('item1', 'data1', 200);
            expect(cache.getCurrentSize()).toBe(200);
            
            cache.set('item2', 'data2', 300);
            expect(cache.getCurrentSize()).toBe(500);
            
            cache.delete('item1');
            expect(cache.getCurrentSize()).toBe(300);
        });

        test('应该拒绝过大的单个项目', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            cache.set('huge-item', 'data', 2000); // 超过最大缓存大小
            
            expect(cache.has('huge-item')).toBe(false);
            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('项目太大无法缓存')
            );
            
            consoleSpy.mockRestore();
        });

        test('应该能够更新最大缓存大小', () => {
            cache.set('item1', 'data1', 500);
            cache.set('item2', 'data2', 400);
            
            expect(cache.getCurrentSize()).toBe(900);
            
            // 减小最大大小，应该触发清理
            cache.setMaxSize(600);
            
            expect(cache.getMaxSize()).toBe(600);
            expect(cache.getCurrentSize()).toBeLessThanOrEqual(600);
        });
    });

    describe('统计信息', () => {
        test('应该正确跟踪命中率', () => {
            cache.set('item1', 'data1', 100);
            
            // 命中
            cache.get('item1');
            cache.get('item1');
            
            // 未命中
            cache.get('non-existent');
            
            const stats = cache.getStats();
            
            expect(stats.hits).toBe(2);
            expect(stats.misses).toBe(1);
            expect(stats.totalAccesses).toBe(3);
            expect(stats.hitRate).toBe('66.67%');
        });

        test('应该提供详细的统计信息', () => {
            cache.set('item1', 'data1', 200);
            cache.set('item2', 'data2', 300);
            
            const stats = cache.getStats();
            
            expect(stats.currentSize).toBe(500);
            expect(stats.maxSize).toBe(1024);
            expect(stats.itemCount).toBe(2);
            expect(stats.averageItemSize).toBe(250);
            expect(stats.usagePercent).toBe('48.83%');
        });
    });

    describe('清理功能', () => {
        test('应该能够执行LRU清理', () => {
            cache.set('item1', 'data1', 200);
            cache.set('item2', 'data2', 200);
            cache.set('item3', 'data3', 200);
            
            // 访问item3，使其成为最近使用的
            cache.get('item3');
            
            const freedSize = cache.performLRUCleanup(300);
            
            expect(freedSize).toBeGreaterThanOrEqual(300);
            expect(cache.has('item3')).toBe(true); // 最近使用的应该保留
        });

        test('应该能够根据条件清理', () => {
            cache.set('keep1', 'data1', 100);
            cache.set('remove1', 'data2', 100);
            cache.set('keep2', 'data3', 100);
            cache.set('remove2', 'data4', 100);
            
            // 清理键名包含'remove'的项目
            const cleanedCount = cache.cleanupByCondition(
                item => item.key.includes('remove')
            );
            
            expect(cleanedCount).toBe(2);
            expect(cache.has('keep1')).toBe(true);
            expect(cache.has('keep2')).toBe(true);
            expect(cache.has('remove1')).toBe(false);
            expect(cache.has('remove2')).toBe(false);
        });

        test('应该能够清理过期项目', () => {
            // 模拟时间
            const originalNow = Date.now;
            let currentTime = 1000000;
            Date.now = jest.fn(() => currentTime);
            
            cache.set('old-item', 'data1', 100);
            
            // 前进时间
            currentTime += 31 * 60 * 1000; // 31分钟后
            
            cache.set('new-item', 'data2', 100);
            
            const cleanedCount = cache.cleanupExpired(30 * 60 * 1000); // 30分钟
            
            expect(cleanedCount).toBe(1);
            expect(cache.has('old-item')).toBe(false);
            expect(cache.has('new-item')).toBe(true);
            
            // 恢复原始Date.now
            Date.now = originalNow;
        });

        test('应该能够清理空闲项目', () => {
            const originalNow = Date.now;
            let currentTime = 1000000;
            Date.now = jest.fn(() => currentTime);
            
            cache.set('active-item', 'data1', 100);
            cache.set('idle-item', 'data2', 100);
            
            // 访问active-item
            cache.get('active-item');
            
            // 前进时间
            currentTime += 11 * 60 * 1000; // 11分钟后
            
            const cleanedCount = cache.cleanupIdle(10 * 60 * 1000); // 10分钟
            
            expect(cleanedCount).toBe(1);
            expect(cache.has('active-item')).toBe(true);
            expect(cache.has('idle-item')).toBe(false);
            
            Date.now = originalNow;
        });
    });

    describe('项目信息', () => {
        test('应该提供详细的项目信息', () => {
            const originalNow = Date.now;
            const fixedTime = 1000000;
            Date.now = jest.fn(() => fixedTime);
            
            cache.set('info-test', 'data', 150);
            cache.get('info-test'); // 增加访问次数
            
            const itemInfo = cache.getItemInfo('info-test');
            
            expect(itemInfo).toEqual({
                key: 'info-test',
                size: 150,
                accessCount: 1,
                createdAt: fixedTime,
                lastAccessed: fixedTime,
                age: 0,
                timeSinceLastAccess: 0
            });
            
            Date.now = originalNow;
        });

        test('应该返回null对于不存在的项目', () => {
            const itemInfo = cache.getItemInfo('non-existent');
            expect(itemInfo).toBeNull();
        });

        test('应该提供所有项目的信息', () => {
            cache.set('item1', 'data1', 100);
            cache.set('item2', 'data2', 200);
            
            const allItems = cache.getAllItemsInfo();
            
            expect(allItems).toHaveLength(2);
            expect(allItems[0].key).toBe('item1');
            expect(allItems[1].key).toBe('item2');
        });
    });

    describe('清空缓存', () => {
        test('应该能够清空所有缓存', () => {
            const mockDispose = jest.fn();
            
            cache.set('item1', { dispose: mockDispose }, 100);
            cache.set('item2', 'data2', 200);
            
            expect(cache.getCurrentSize()).toBe(300);
            
            cache.clear();
            
            expect(cache.getCurrentSize()).toBe(0);
            expect(cache.keys()).toHaveLength(0);
            expect(mockDispose).toHaveBeenCalled();
        });
    });
});
