/**
 * bitECS系统集成
 * 为Cantos MMORPG项目提供基于bitECS的ECS架构实现
 * 基于bitECS 0.4.0 API
 */

import {
    createWorld,
    addEntity,
    removeEntity,
    addComponent,
    removeComponent,
    hasComponent,
    query,
    entityExists
} from 'bitecs';

/**
 * 预定义的组件类型
 * 根据MMORPG游戏需求定义常用组件
 * 使用SoA (Structure of Arrays) 格式以获得最佳性能
 */

// 变换组件 - 位置、旋转、缩放
export const Transform = {
    x: [],              // X坐标
    y: [],              // Y坐标
    z: [],              // Z坐标
    rotationX: [],      // X轴旋转
    rotationY: [],      // Y轴旋转
    rotationZ: [],      // Z轴旋转
    scaleX: [],         // X轴缩放
    scaleY: [],         // Y轴缩放
    scaleZ: []          // Z轴缩放
};

// 渲染组件 - 3D模型渲染相关
export const Render = {
    meshId: [],         // 网格ID
    materialId: [],     // 材质ID
    visible: [],        // 是否可见 (0/1)
    castShadow: [],     // 是否投射阴影
    receiveShadow: []   // 是否接收阴影
};

// 物理组件 - 物理模拟相关
export const Physics = {
    mass: [],           // 质量
    velocityX: [],      // X轴速度
    velocityY: [],      // Y轴速度
    velocityZ: [],      // Z轴速度
    friction: [],       // 摩擦力
    restitution: [],    // 弹性系数
    isKinematic: [],    // 是否为运动学物体
    isStatic: []        // 是否为静态物体
};

// 动画组件 - 动画播放控制
export const Animation = {
    currentAnimationId: [],  // 当前动画ID
    animationSpeed: [],      // 动画播放速度
    isPlaying: [],           // 是否正在播放
    loop: [],                // 是否循环播放
    startTime: []            // 动画开始时间
};

// 网络组件 - 网络同步相关
export const Network = {
    playerId: [],       // 玩家ID
    isLocal: [],        // 是否为本地玩家
    lastSyncTime: [],   // 上次同步时间
    needsSync: []       // 是否需要同步
};

// 生命值组件 - 角色生命值系统
export const Health = {
    current: [],        // 当前生命值
    maximum: [],        // 最大生命值
    regeneration: []    // 生命值恢复速度
};

// 玩家输入组件 - 玩家输入状态
export const PlayerInput = {
    moveForward: [],    // 前进
    moveBackward: [],   // 后退
    moveLeft: [],       // 左移
    moveRight: [],      // 右移
    jump: [],           // 跳跃
    mouseX: [],         // 鼠标X轴移动
    mouseY: []          // 鼠标Y轴移动
};

/**
 * ECS世界管理器
 * 管理整个ECS系统的生命周期
 * 基于bitECS 0.4.0 API
 */
export class BitECSWorld {
    constructor() {
        this.world = createWorld();
        this.systems = [];
        this.isRunning = false;

        console.log('BitECS世界已创建');
    }

    /**
     * 创建实体
     * @returns {number} 实体ID
     */
    createEntity() {
        const entity = addEntity(this.world);
        console.log(`创建实体: ${entity}`);
        return entity;
    }

    /**
     * 销毁实体
     * @param {number} entity - 实体ID
     */
    destroyEntity(entity) {
        removeEntity(this.world, entity);
        console.log(`销毁实体: ${entity}`);
    }

    /**
     * 为实体添加组件
     * @param {number} entity - 实体ID
     * @param {Object} component - 组件定义
     * @param {Object} data - 组件初始数据
     */
    addComponent(entity, component, data = {}) {
        addComponent(this.world, entity, component);

        // 设置组件数据
        for (const [key, value] of Object.entries(data)) {
            if (component[key] !== undefined) {
                component[key][entity] = value;
            }
        }

        console.log(`为实体 ${entity} 添加组件`);
    }

    /**
     * 从实体移除组件
     * @param {number} entity - 实体ID
     * @param {Object} component - 组件定义
     */
    removeComponent(entity, component) {
        removeComponent(this.world, entity, component);
        console.log(`从实体 ${entity} 移除组件`);
    }

    /**
     * 检查实体是否有指定组件
     * @param {number} entity - 实体ID
     * @param {Object} component - 组件定义
     * @returns {boolean}
     */
    hasComponent(entity, component) {
        return hasComponent(this.world, entity, component);
    }

    /**
     * 注册系统
     * @param {Function} system - 系统函数
     * @param {string} name - 系统名称
     */
    registerSystem(system, name = 'UnnamedSystem') {
        this.systems.push({ system, name });
        console.log(`注册系统: ${name}`);
    }

    /**
     * 执行查询
     * @param {Array} components - 组件数组
     * @returns {Array} 实体数组
     */
    query(components) {
        return query(this.world, components);
    }

    /**
     * 执行所有系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isRunning) return;

        for (const { system, name } of this.systems) {
            try {
                system(this.world, deltaTime);
            } catch (error) {
                console.error(`系统 ${name} 执行错误:`, error);
            }
        }
    }

    /**
     * 启动ECS系统
     */
    start() {
        this.isRunning = true;
        console.log('BitECS系统已启动');
    }

    /**
     * 停止ECS系统
     */
    stop() {
        this.isRunning = false;
        console.log('BitECS系统已停止');
    }

    /**
     * 获取世界实例
     * @returns {Object} 世界实例
     */
    getWorld() {
        return this.world;
    }
}

// 导出全局ECS世界实例
export const ecsWorld = new BitECSWorld();

// 暴露到全局作用域以便调试
if (typeof window !== 'undefined') {
    window.ecsWorld = ecsWorld;
    window.BitECSComponents = {
        Transform,
        Render,
        Physics,
        Animation,
        Network,
        Health,
        PlayerInput
    };
}

console.log('bitECS系统模块已加载');
