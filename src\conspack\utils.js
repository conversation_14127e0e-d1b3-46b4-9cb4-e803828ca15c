
/**
 * If `ENCODE_EMPTY_VECTOR` is false, encode/decode empty vector [] will signal an error.
 * In cl, empty lisp () is NIL which is encoded as a bool value, but we cannot do this in js.
 * So, it's recommened to set this value to false if msgs are sent to a lisp program.
 */
const ENCODE_EMPTY_VECTOR = false;

class Utils {
    /** Set ENCODE_EMPTY_VECTOR to true */
    static turn_on_empty_vector_encoding() {
        ENCODE_EMPTY_VECTOR = true;
    }

    /** Set ENCODE_EMPTY_VECTOR to false */
    static turn_off_empty_vector_encoding() {
        ENCODE_EMPTY_VECTOR = false;
    }

    /** Restore ENCODE_EMPTY_VECTOR to false */
    static restore_empty_vector_encoding() {
        ENCODE_EMPTY_VECTOR = false;
    }

    /**
     * 测试某个整数是否BigInt类型
     * @param {number} intVal
     * @returns {boolean}
     */
    static js_big_int_p(intVal) {
        return !Number.isSafeInteger(intVal);
    }

    /**
     * 获得一个无符号整数的字节码数组, 注意, 为了方便迭代, 返回的数字与整数二进制的字节顺序相反
     * 例如, 305419896 = 0x12345678 => [78, 56, 34, 12]
     * 副作用: 无
     * @param {uint} intVal 无符号整数
     * @param {uint} size 这个整数的字节个数
     * @returns {uint8[]}
     */
    static get_unsigned_int_byte_array(intVal, size) { // 注意对于数字类型, js是传值而不是传应用, 因此不会修改原值
        const mask = 0b11111111; // 0xFF
        let arr = Array(size);
        for(let i = 0; i < size; i++) {
            arr[i] = intVal & mask;
            intVal = intVal >> 8;
        }
        return arr;
    }

    /**
     * get_unsigned_int_byte_array的BigInt版本
     * @param {bigint} intVal
     * @param {uint} size
     * @returns {uint8[]}
     */
    static get_unsigned_bigint_byte_array(intVal, size) {
        const mask = 0b11111111n;
        const arr = Array(size);
        for(let i = 0; i < size; i++) {
            arr[i] = Number(intVal & mask);
            intVal = intVal >> 8n;
        }
        return arr;
    }

    // https://stackoverflow.com/questions/966225/how-can-i-create-a-two-dimensional-array-in-javascript
    // createArray(2, 3, 4)
    static createArray(length) {
        var arr = new Array(length || 0),
            i = length;

        if (arguments.length > 1) {
            var args = Array.prototype.slice.call(arguments, 1);
            while(i--) arr[length-1 - i] = this.createArray.apply(this, args);
        }

        return arr;
    }

    // https://stackoverflow.com/questions/20477177/creating-an-array-of-cumulative-sum-in-javascript
    static cumulativeSum(n)     { return (sum =>  value =>  sum += value)(n) }; // [5, 10, 3, 2].map(cumulativeSum(0)) // > [5, 15, 18, 20]
    static cumulativeProduct(n) { return (prod => value => prod *= value)(n) };

    /**
     * 用一维向量初始化多维矩阵.
     * Utils.fillMatrix(Utils.createArray(2,3,4), [2,3,4], Array(24).fill(0));
     * @param {any[]} matrix 以数组表示的矩阵
     * @param {any[]} dimensions 按CL的习惯命名数组的维度, [2, 3, 4]表示2*3*4的矩阵, mathjs中则称为size并通过math.size()获得
     * @param {any[]} valueVector 一维数组, 其元数个数等于dimensions各数之积
     * @returns {any[]} 已填充好的matrix矩阵
     */
    static fillMatrix(matrix, dimensions, valueVector) {

        const getPos = (nth_idx, size) => {
            let rem = nth_idx % size;
            let floor = (nth_idx - rem) / size;
            return [floor, rem]
        }

        const goDeep = (matrix, idx) => {
            let curr = matrix;
            for(let i of idx) {
                curr = curr[i];
            }
            return curr;
        }

        const count = valueVector.length;
        // (cdr (mapcar (lambda (lst) (apply #'* lst)) (mapcon #'list '(2 3 4))))
        const accu = dimensions.reverse().map(this.cumulativeProduct(1)).reverse().slice(1); // get [12, 4] for 2*3*4 matrix
        for(let i = 0; i < count; i++) {
            let indx = [];
            let nth = i;
            let pos = null;
            for(let num of accu) {
                pos = getPos(nth, num);
                indx.push(pos[0]);
                nth = pos[1];
            }
            goDeep(matrix, indx)[pos[1]] = valueVector[i];
        }
        return matrix;
    }
}

export default Utils;
export { ENCODE_EMPTY_VECTOR };
