// src/core/assets/AssetManager.unit.test.js
// 资源管理系统单元测试

import { jest } from '@jest/globals';
import AssetManager, { AssetType, AssetStatus } from './AssetManager.js';

// Mock Babylon.js 模块
jest.mock('@babylonjs/core', () => ({
    SceneLoader: {
        ImportMesh: jest.fn()
    },
    Texture: jest.fn(),
    Sound: jest.fn(),
    Tools: {
        LoadFile: jest.fn()
    }
}));

// Mock fetch
global.fetch = jest.fn();

describe('AssetManager', () => {
    let assetManager;
    let mockScene;

    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();

        // 创建资源管理器实例
        assetManager = new AssetManager({
            maxCacheSize: 10 * 1024 * 1024, // 10MB用于测试
            enableAutoGC: false, // 测试时禁用自动GC
            retryAttempts: 2
        });

        // 创建模拟场景对象
        mockScene = {
            dispose: jest.fn(),
            getPhysicsEngine: jest.fn(() => null)
        };
    });

    afterEach(() => {
        if (assetManager) {
            assetManager.dispose();
        }
    });

    describe('构造函数', () => {
        test('应该正确初始化默认配置', () => {
            const manager = new AssetManager();

            expect(manager.options.maxCacheSize).toBe(100 * 1024 * 1024);
            expect(manager.options.enableAutoGC).toBe(true);
            expect(manager.options.retryAttempts).toBe(3);
            expect(manager.cache).toBeDefined();
            expect(manager.loaders).toBeDefined();
            expect(manager.progressManager).toBeDefined();
        });

        test('应该正确应用自定义配置', () => {
            const customOptions = {
                maxCacheSize: 50 * 1024 * 1024,
                enableAutoGC: false,
                retryAttempts: 1
            };

            const manager = new AssetManager(customOptions);

            expect(manager.options.maxCacheSize).toBe(50 * 1024 * 1024);
            expect(manager.options.enableAutoGC).toBe(false);
            expect(manager.options.retryAttempts).toBe(1);
        });
    });

    describe('资源加载', () => {
        test('应该能够加载配置文件', async () => {
            const mockConfigData = { name: 'test', value: 123 };

            fetch.mockResolvedValueOnce({
                ok: true,
                json: jest.fn().mockResolvedValue(mockConfigData)
            });

            const result = await assetManager.loadConfig('test-config.json');

            expect(result).toBeDefined();
            expect(result.data).toEqual(mockConfigData);
            expect(result.type).toBe(AssetType.CONFIG);
            expect(result.url).toBe('test-config.json');
        });

        test('应该处理加载失败的情况', async () => {
            fetch.mockRejectedValueOnce(new Error('网络错误'));

            await expect(assetManager.loadConfig('invalid-config.json'))
                .rejects.toThrow('网络错误');
        });

        test('应该支持重试机制', async () => {
            // 第一次失败，第二次成功
            fetch
                .mockRejectedValueOnce(new Error('临时错误'))
                .mockResolvedValueOnce({
                    ok: true,
                    json: jest.fn().mockResolvedValue({ success: true })
                });

            const result = await assetManager.loadConfig('retry-test.json');

            expect(result).toBeDefined();
            expect(result.data.success).toBe(true);
            expect(fetch).toHaveBeenCalledTimes(2);
        });
    });

    describe('缓存管理', () => {
        test('应该缓存已加载的资源', async () => {
            const mockData = { cached: true };

            fetch.mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue(mockData)
            });

            // 第一次加载
            const result1 = await assetManager.loadConfig('cache-test.json');
            expect(result1.data).toEqual(mockData);

            // 第二次加载应该从缓存获取
            const result2 = await assetManager.loadConfig('cache-test.json');
            expect(result2.data).toEqual(mockData);

            // fetch应该只被调用一次
            expect(fetch).toHaveBeenCalledTimes(1);

            // 统计信息应该显示缓存命中
            const stats = assetManager.getStats();
            expect(stats.cacheHits).toBe(1);
            expect(stats.cacheMisses).toBe(1);
        });

        test('应该能够释放缓存的资源', async () => {
            const mockData = { test: true };

            fetch.mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue(mockData)
            });

            // 加载资源
            await assetManager.loadConfig('release-test.json');

            // 验证资源已缓存
            expect(assetManager.getAsset('release-test.json')).toBeDefined();

            // 释放资源
            assetManager.releaseAsset('release-test.json');

            // 验证资源已被释放
            expect(assetManager.getAsset('release-test.json')).toBeNull();
        });
    });

    describe('预加载功能', () => {
        test('应该能够批量预加载资源', async () => {
            const assetList = [
                { url: 'config1.json', type: AssetType.CONFIG },
                { url: 'config2.json', type: AssetType.CONFIG }
            ];

            fetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: jest.fn().mockResolvedValue({ id: 1 })
                })
                .mockResolvedValueOnce({
                    ok: true,
                    json: jest.fn().mockResolvedValue({ id: 2 })
                });

            const result = await assetManager.preloadAssets(assetList, {
                concurrent: 2
            });

            expect(result.loaded).toHaveLength(2);
            expect(result.failed).toHaveLength(0);
            expect(result.total).toBe(2);
        });

        test('应该处理预加载中的失败情况', async () => {
            const assetList = [
                { url: 'success.json', type: AssetType.CONFIG },
                { url: 'failure.json', type: AssetType.CONFIG }
            ];

            fetch
                .mockResolvedValueOnce({
                    ok: true,
                    json: jest.fn().mockResolvedValue({ success: true })
                })
                .mockRejectedValueOnce(new Error('加载失败'));

            const result = await assetManager.preloadAssets(assetList);

            expect(result.loaded).toHaveLength(1);
            expect(result.failed).toHaveLength(1);
            expect(result.total).toBe(2);
        });
    });

    describe('进度跟踪', () => {
        test('应该提供加载进度信息', () => {
            const progress = assetManager.getLoadingProgress();

            expect(progress).toBeDefined();
            expect(progress.isLoading).toBe(false);
            expect(progress.activeItems).toBe(0);
        });
    });

    describe('统计信息', () => {
        test('应该提供详细的统计信息', async () => {
            fetch.mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue({ test: true })
            });

            await assetManager.loadConfig('stats-test.json');

            const stats = assetManager.getStats();

            expect(stats.totalLoaded).toBe(1);
            expect(stats.totalFailed).toBe(0);
            expect(stats.cacheSize).toBeGreaterThan(0);
            expect(stats.registeredAssets).toBe(1);
        });
    });

    describe('错误处理', () => {
        test('应该能够注册和使用错误处理器', async () => {
            const errorHandler = jest.fn().mockResolvedValue({ fallback: true });

            assetManager.registerErrorHandler(AssetType.CONFIG, errorHandler);

            fetch.mockRejectedValue(new Error('加载失败'));

            const result = await assetManager.loadConfig('error-test.json');

            expect(errorHandler).toHaveBeenCalled();
            expect(result.fallback).toBe(true);
        });
    });

    describe('事件系统', () => {
        test('应该触发资源加载事件', async () => {
            const loadStartSpy = jest.fn();
            const loadedSpy = jest.fn();

            assetManager.on('assetLoadStart', loadStartSpy);
            assetManager.on('assetLoaded', loadedSpy);

            fetch.mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue({ test: true })
            });

            await assetManager.loadConfig('event-test.json');

            expect(loadStartSpy).toHaveBeenCalled();
            expect(loadedSpy).toHaveBeenCalled();
        });

        test('应该触发资源加载失败事件', async () => {
            const errorSpy = jest.fn();

            assetManager.on('assetLoadError', errorSpy);

            fetch.mockRejectedValue(new Error('加载失败'));

            try {
                await assetManager.loadConfig('error-event-test.json');
            } catch (error) {
                // 预期的错误
            }

            expect(errorSpy).toHaveBeenCalled();
        });
    });

    describe('资源清理', () => {
        test('应该正确清理所有资源', async () => {
            fetch.mockResolvedValue({
                ok: true,
                json: jest.fn().mockResolvedValue({ test: true })
            });

            await assetManager.loadConfig('cleanup-test.json');

            expect(assetManager.getStats().registeredAssets).toBe(1);

            assetManager.dispose();

            // 验证清理后的状态
            expect(assetManager.assetRegistry.size).toBe(0);
            expect(assetManager.cache.getCurrentSize()).toBe(0);
        });
    });
});

describe('AssetManager 集成测试', () => {
    let assetManager;

    beforeEach(() => {
        assetManager = new AssetManager({
            maxCacheSize: 1024 * 1024, // 1MB
            enableAutoGC: true,
            gcThreshold: 0.5 // 50%触发GC
        });
    });

    afterEach(() => {
        if (assetManager) {
            assetManager.dispose();
        }
    });

    test('应该在缓存满时触发垃圾回收', async () => {
        const gcSpy = jest.fn();
        assetManager.on('garbageCollected', gcSpy);

        // 模拟大文件加载
        const largeData = 'x'.repeat(600 * 1024); // 600KB

        fetch.mockResolvedValue({
            ok: true,
            json: jest.fn().mockResolvedValue({ data: largeData })
        });

        // 加载多个大文件，触发GC
        await assetManager.loadConfig('large1.json');
        await assetManager.loadConfig('large2.json');

        // 应该触发垃圾回收
        expect(gcSpy).toHaveBeenCalled();
    });

    test('应该正确处理并发加载', async () => {
        fetch.mockImplementation((url) => {
            return Promise.resolve({
                ok: true,
                json: jest.fn().mockResolvedValue({ url })
            });
        });

        // 并发加载多个资源
        const promises = [
            assetManager.loadConfig('concurrent1.json'),
            assetManager.loadConfig('concurrent2.json'),
            assetManager.loadConfig('concurrent3.json')
        ];

        const results = await Promise.all(promises);

        expect(results).toHaveLength(3);
        results.forEach((result, index) => {
            expect(result.data.url).toBe(`concurrent${index + 1}.json`);
        });
    });
});
