/**
 * PhysicsUtils 单元测试
 * 测试物理工具类的所有功能
 */

import { jest } from '@jest/globals';
import { Vector3 } from "@babylonjs/core/Maths/math.vector";
import { Color3 } from "@babylonjs/core/Maths/math.color";
import { PhysicsUtils, PhysicsBodyPresets } from './PhysicsUtils.js';

// Mock Babylon.js 模块
jest.mock('@babylonjs/core/Meshes/meshBuilder', () => ({
    MeshBuilder: {
        CreateBox: jest.fn(() => ({
            position: Vector3.Zero(),
            rotation: Vector3.Zero(),
            scaling: Vector3.One(),
            material: null
        })),
        CreateSphere: jest.fn(() => ({
            position: Vector3.Zero(),
            rotation: Vector3.Zero(),
            scaling: Vector3.One(),
            material: null
        })),
        CreateGround: jest.fn(() => ({
            position: Vector3.Zero(),
            rotation: Vector3.Zero(),
            scaling: Vector3.One(),
            material: null
        })),
        CreateCapsule: jest.fn(() => ({
            position: Vector3.Zero(),
            rotation: Vector3.Zero(),
            scaling: Vector3.One(),
            material: null
        }))
    }
}));

jest.mock('@babylonjs/core/Materials/standardMaterial', () => ({
    StandardMaterial: jest.fn().mockImplementation(() => ({
        diffuseColor: Color3.Gray()
    }))
}));

// Mock ECS 系统
const mockECSWorld = {
    createEntity: jest.fn(() => 1),
    addComponent: jest.fn()
};

// Mock 物理管理器
const mockPhysicsManager = {
    isReady: jest.fn(() => true),
    createRigidBody: jest.fn(() => ({ id: 'mock_body' })),
    createTrigger: jest.fn(() => ({ id: 'mock_trigger' })),
    physicsBodies: new Map(),
    generateBodyId: jest.fn(() => 'mock_body_id')
};

// Mock 场景
const mockScene = {
    dispose: jest.fn()
};

describe('PhysicsUtils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockPhysicsManager.physicsBodies.clear();
    });

    describe('createPhysicsEntity', () => {
        test('应该创建基本的物理实体', () => {
            const result = PhysicsUtils.createPhysicsEntity(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                {
                    name: 'TestEntity',
                    position: new Vector3(1, 2, 3)
                }
            );

            expect(result).toBeDefined();
            expect(result.entity).toBe(1);
            expect(result.name).toBe('TestEntity');
            expect(mockECSWorld.createEntity).toHaveBeenCalled();
            expect(mockECSWorld.addComponent).toHaveBeenCalledTimes(2); // Transform + Physics
        });

        test('应该使用指定的预设', () => {
            const result = PhysicsUtils.createPhysicsEntity(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                {
                    preset: 'PLAYER',
                    meshType: 'capsule'
                }
            );

            expect(result).toBeDefined();
            expect(mockPhysicsManager.createRigidBody).toHaveBeenCalled();
        });

        test('应该创建触发器', () => {
            const result = PhysicsUtils.createPhysicsEntity(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                {
                    preset: 'TRIGGER',
                    customPhysicsOptions: {
                        isTrigger: true,
                        triggerCallback: jest.fn()
                    }
                }
            );

            expect(result).toBeDefined();
            expect(mockPhysicsManager.createTrigger).toHaveBeenCalled();
        });

        test('应该处理物理管理器未准备就绪的情况', () => {
            mockPhysicsManager.isReady.mockReturnValue(false);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            const result = PhysicsUtils.createPhysicsEntity(
                mockECSWorld,
                mockPhysicsManager,
                mockScene
            );

            expect(result).toBeDefined();
            expect(result.physicsBody).toBeNull();
            consoleSpy.mockRestore();
        });

        test('应该处理创建失败的情况', () => {
            mockECSWorld.createEntity.mockImplementation(() => {
                throw new Error('创建实体失败');
            });
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            const result = PhysicsUtils.createPhysicsEntity(
                mockECSWorld,
                mockPhysicsManager,
                mockScene
            );

            expect(result).toBeNull();
            expect(consoleSpy).toHaveBeenCalled();
            consoleSpy.mockRestore();
        });
    });

    describe('createPhysicsGround', () => {
        test('应该创建物理地面', () => {
            const result = PhysicsUtils.createPhysicsGround(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                {
                    width: 100,
                    height: 100,
                    y: -1
                }
            );

            expect(result).toBeDefined();
            expect(result.name).toBe('Ground');
        });

        test('应该使用默认参数', () => {
            const result = PhysicsUtils.createPhysicsGround(
                mockECSWorld,
                mockPhysicsManager,
                mockScene
            );

            expect(result).toBeDefined();
        });
    });

    describe('createPhysicsBall', () => {
        test('应该创建物理球体', () => {
            const position = new Vector3(5, 10, 5);
            const result = PhysicsUtils.createPhysicsBall(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                position,
                {
                    color: Color3.Red()
                }
            );

            expect(result).toBeDefined();
            expect(result.name).toBe('Ball');
        });
    });

    describe('createPhysicsBox', () => {
        test('应该创建物理箱子', () => {
            const position = new Vector3(0, 5, 0);
            const result = PhysicsUtils.createPhysicsBox(
                mockECSWorld,
                mockPhysicsManager,
                mockScene,
                position,
                {
                    color: Color3.Blue()
                }
            );

            expect(result).toBeDefined();
            expect(result.name).toBe('Box');
        });
    });

    describe('工具函数', () => {
        test('generatePhysicsBodyId 应该生成唯一ID', () => {
            const id1 = PhysicsUtils.generatePhysicsBodyId();
            const id2 = PhysicsUtils.generatePhysicsBodyId();

            expect(typeof id1).toBe('string');
            expect(typeof id2).toBe('string');
            expect(id1).not.toBe(id2);
            expect(id1).toMatch(/^physics_\d+_[a-z0-9]+$/);
        });

        test('distance 应该计算两点间距离', () => {
            const point1 = new Vector3(0, 0, 0);
            const point2 = new Vector3(3, 4, 0);
            const distance = PhysicsUtils.distance(point1, point2);

            expect(distance).toBe(5); // 3-4-5 直角三角形
        });

        test('degreesToRadians 应该正确转换角度到弧度', () => {
            expect(PhysicsUtils.degreesToRadians(0)).toBe(0);
            expect(PhysicsUtils.degreesToRadians(90)).toBeCloseTo(Math.PI / 2);
            expect(PhysicsUtils.degreesToRadians(180)).toBeCloseTo(Math.PI);
            expect(PhysicsUtils.degreesToRadians(360)).toBeCloseTo(2 * Math.PI);
        });

        test('radiansToDegrees 应该正确转换弧度到角度', () => {
            expect(PhysicsUtils.radiansToDegrees(0)).toBe(0);
            expect(PhysicsUtils.radiansToDegrees(Math.PI / 2)).toBeCloseTo(90);
            expect(PhysicsUtils.radiansToDegrees(Math.PI)).toBeCloseTo(180);
            expect(PhysicsUtils.radiansToDegrees(2 * Math.PI)).toBeCloseTo(360);
        });
    });
});

describe('PhysicsBodyPresets', () => {
    test('应该包含所有预定义的物理体预设', () => {
        expect(PhysicsBodyPresets.PLAYER).toBeDefined();
        expect(PhysicsBodyPresets.BOX).toBeDefined();
        expect(PhysicsBodyPresets.BALL).toBeDefined();
        expect(PhysicsBodyPresets.GROUND).toBeDefined();
        expect(PhysicsBodyPresets.TRIGGER).toBeDefined();
    });

    test('PLAYER预设应该有正确的属性', () => {
        const player = PhysicsBodyPresets.PLAYER;
        expect(player.mass).toBe(70);
        expect(player.friction).toBe(0.8);
        expect(player.restitution).toBe(0.1);
        expect(player.shapeType).toBe('capsule');
        expect(player.shapeOptions).toBeDefined();
    });

    test('BOX预设应该有正确的属性', () => {
        const box = PhysicsBodyPresets.BOX;
        expect(box.mass).toBe(10);
        expect(box.shapeType).toBe('box');
        expect(box.shapeOptions.size).toBeDefined();
    });

    test('BALL预设应该有正确的属性', () => {
        const ball = PhysicsBodyPresets.BALL;
        expect(ball.mass).toBe(5);
        expect(ball.shapeType).toBe('sphere');
        expect(ball.shapeOptions.radius).toBe(0.5);
    });

    test('GROUND预设应该是静态的', () => {
        const ground = PhysicsBodyPresets.GROUND;
        expect(ground.mass).toBe(0);
        expect(ground.motionType).toBe('STATIC');
        expect(ground.shapeType).toBe('box');
    });

    test('TRIGGER预设应该是触发器', () => {
        const trigger = PhysicsBodyPresets.TRIGGER;
        expect(trigger.mass).toBe(0);
        expect(trigger.motionType).toBe('STATIC');
        expect(trigger.isTrigger).toBe(true);
    });
});

console.log('PhysicsUtils单元测试已加载');
