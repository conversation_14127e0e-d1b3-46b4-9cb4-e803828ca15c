import Utils from "./utils";

// Ported from fast-io/src/io.lisp

const DEFAULT_BUFF_SIZE = 8192;
const BUFF_SIZE_ADJUST_THRESHOLD = 0.8;

/**
 * 无符号型整数转换成有符号型整数.
 * 副作用: 无.
 * @param {int} value
 * @param {int} size
 * @returns {int}
 */
function unsigned_to_signed(value, size) {
    let max_signed = 2 ** (8 * size - 1 );
    let to_subtract = 2 ** (8 * size);
    return (value >= max_signed) ? value - to_subtract : value;
}

/**
 * 大整数的幂运算, 因为Math.pow()的参数是number类型.
 * @param {bigint} base
 * @param {bigint} n
 * @param {bigint} accu
 * @returns {bigint}
 */
function bigint_pow(base, n, accu = base) {
    return n === 1n ? accu : bigint_pow(base, n - 1n, accu * base);
}

/**
 *
 * @param {bigint} value
 * @param {uint} size
 * @returns {bigint}
 */
function bigint_unsigned_to_signed(value, size) {
    let bigSize = BigInt(size);
    let max_signed = bigint_pow(2n, 8n * bigSize - 1n, 2n);
    let to_subtract = bigint_pow(2n, 8n * bigSize, 2n);
    return (value >= max_signed) ? value - to_subtract : value;
}

function unsigned_to_signed_128(value) {
    let max_signed = 170141183460469231731687303715884105728n;
    let to_subtract = 340282366920938463463374607431768211456n;
    return (value >= max_signed) ? value - to_subtract : value;
}

/**
 * 有符号型整数转换成无符号型整数.
 * 副作用: 无.
 * @param {int} value
 * @param {int} size
 * @returns {int}
 */
function signed_to_unsigned(value, size) {
    return (value < 0) ? value + (2 ** (8 * size)) : value;
}

/**
 * 128位有符号bigint转换成无符号bigint
 * @param {bigint} value
 * @returns
 */
function signed_to_unsigned_128(value) {
    return (value < 0n) ? value + 340282366920938463463374607431768211456n : value;
}

class Writer {
    /** 当前可以写入的缓冲区位置, [0,  buffIndex)的缓冲字节都已写满, 注意buffIndex处尚未写入. */
    buffIndex;
    /** ArrayBuffer */
    buffer;
    /** DataView */
    dataView;
    textEncoder;
    /** 返回缓冲区长度
     * @returns {uint}
    */
    get buffSize() {
        return this.buffer.byteLength;
    }

    constructor(buffSize = DEFAULT_BUFF_SIZE) {
        this.buffer = new ArrayBuffer(buffSize);
        this.dataView = new DataView(this.buffer);
        this.textEncoder = new TextEncoder();
        this.buffIndex = 0;
    }

    toString(base = 16) {
        let str = "ArrayBuffer { [Uint8Contents]: <";
        const maxIdx = this.buffIndex - 1;
        for(let i = 0; i <= maxIdx; i++) {
            str += this.dataView.getUint8(i).toString(base);
            if(i < maxIdx) { str+= ' '; }
        }
        str += '>, buffIndex: ' + this.buffIndex.toString(10);
        str += ', bufferSize: ' + this.buffSize.toString(10);
        return str;
    }

    /**
     * 缓冲大小必须能够容纳新数据, 调整新缓冲大小是默认缓冲的最小整数倍, 原缓冲数据会拷贝到新缓冲.
     * 副作用, this.buffer被替换成一个更长的ArrayBuffer, 创建新的this.dataView对象.
     * @param {int} incomingSize 将要写入缓冲区的字节数
     * @returns {int} 返回新的ArrayBuffer的长度
    */
    resizeBuffer(incomingSize) {
        let adjustFactor = Math.ceil((this.buffIndex + incomingSize) / DEFAULT_BUFF_SIZE);
        let newSize = adjustFactor * DEFAULT_BUFF_SIZE;
        let newBuffer = new ArrayBuffer(newSize);
        new Uint8Array(newBuffer).set(new Uint8Array(this.buffer));
        this.buffer = newBuffer;
        this.dataView = new DataView(newBuffer); // dataView.buffer is readonly
        return newSize;
    }

    /**
     * 在编码结束后调用, 创建一个两倍长的新缓冲区.
     * 副作用, this.buffer被替换成一个两倍长的ArrayBuffer, 创建新的this.dataView对象.
    */
    resize_buffer_silent() {
        let newSize = this.buffer.byteLength << 1;
        let newBuffer = new ArrayBuffer(newSize);
        this.buffer = newBuffer;
        this.dataView = new DataView(newBuffer);
    }

    /**
     * 根据本次缓冲使用量判断是否增加缓冲.
     * 副作用: 无.
     */
    should_enlarge_buffer() {
        return this.buffIndex >= this.buffer.byteLength * BUFF_SIZE_ADJUST_THRESHOLD ? true : false;
    }

    /**
     * 重置当前索引.
     * 副作用: this.buffIndex设置为0.
     */
    #nReset() {
        this.buffIndex = 0;
        return true;
    }

    /**
     * 向缓冲区写入字符串的utf8字节编码.
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {Uint8Array[]} byteArray 字符串编码后的字节向量
     * @param {uint} size 字节向量的长度, 或者未定义
     */
    write_text_bytes(byteArray, size) {
        if (this.buffIndex + size <= this.buffSize) { // this will always have one free byte
            for(let i = 0; i < size; i++) {
                this.dataView.setUint8(this.buffIndex++, byteArray[i]);
            }
        }
        else {
            this.resizeBuffer(size);
            this.write_text_bytes(byteArray, size);
        }
    }

    /**
     * 获取缓冲区的有效数据. 与take的区别是, take不会将this.buffIndex设置为0.
     * 一次性获取缓冲区的有效数据, 并且重置当前索引.
     * 副作用: this.buffIndex归零.
     */
    ntake() {
        let content = this.buffer.slice(0, this.buffIndex);
        this.#nReset();
        return content;
    }

    /**
     * 一次性获取缓冲区的有效数据. 与nTake的区别是, take不会将this.buffIndex设置为0.
     * 副作用: 无.
     */
    take() {
        return this.buffer.slice(0, this.buffIndex);
    }

    /**
     * 向缓冲区写入Int8范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_write8(value) {
        const valueSize = 1; // byte
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setInt8(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write8(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入UInt8范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_writeu8(value) {
        const valueSize = 1; // byte
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setUint8(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_writeu8(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Int16范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_write16(value) {
        const valueSize = 2; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setInt16(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write16(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入UInt16范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_writeu16(value) {
        const valueSize = 2; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setUint16(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_writeu16(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Int32范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_write32(value) {
        const valueSize = 4; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setInt32(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write32(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入UInt32范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int} value
     * @returns {int}
     */
    dataview_writeu32(value) {
        const valueSize = 4; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setUint32(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_writeu32(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Int64范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int | bigint} value
     * @returns {int}
     */
    dataview_write64(value) {
        let valueSize = 8; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setBigInt64(this.buffIndex, BigInt(value));
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write64(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入UInt64范围内的整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int | bigint} value
     * @returns {int}
     */
    dataview_writeu64(value) {
        let valueSize = 8; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setBigUint64(this.buffIndex, BigInt(value));
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_writeu64(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Float32精度的浮点数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {number} value
     * @returns {number}
     */
    dataview_write_single(value) {
        let valueSize = 4; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setFloat32(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write_single(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Float64精度的浮点数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {number} value
     * @returns {number}
     */
    dataview_write_double(value) {
        let valueSize = 8; // bytes
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setFloat64(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.dataview_write_double(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入Int8整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {int8} value
     * @returns {int}
     */
    write8(value) {
        const valueSize = 1; // 1表示写入的value字节个数
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setInt8(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.write8(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入UInt8整数
     * 副作用: 写入this.buffer, 更新this.buffIndex.
     * @param {uint8} value
     * @returns {int}
     */
    writeu8(value) {
        const valueSize = 1; // 1表示写入的value字节个数
        if (this.buffIndex + valueSize <= this.buffSize) {
            this.dataView.setUint8(this.buffIndex, value);
            this.buffIndex += valueSize;
        }
        else {
            this.resizeBuffer(valueSize);
            this.writeu8(value);
        }
        return value;
    }

    /**
     * 向缓冲区写入无符号大端整数
     * @param {uint} value 要序列号的无符号整数
     * @param {uint} size 整数的字节个数
     * @returns {uint8[]}
     */
    write_unsigned_be(value, size) {
        let byteArray = Utils.get_unsigned_int_byte_array(value, size);
        for(let i = size - 1; i >= 0; i--) {
            this.writeu8(byteArray[i]);
        }
        return byteArray;
    }

    write_unsigned_bigint_be(value, size) {
        let byteArray = Utils.get_unsigned_bigint_byte_array(value, size);
        for(let i = size - 1; i >= 0; i--) {
            this.writeu8(byteArray[i]);
        }
        return byteArray;
    }

    /**
     * 向缓冲区写入无符号小端整数
     * @param {uint} value 要序列号的无符号整数
     * @param {uint} size 整数的字节个数
     * @returns {uint8[]}
     */
    write_unsigned_le(value, size) {
        byteArray = Utils.get_unsigned_int_byte_array(value, size);
        for(let i = 0; i <= size - 1; i++) {
            this.writeu8(byteArray[i]);
        }
        return byteArray;
    }

    write_unsigned_bigint_le(value, size) {
        byteArray = Utils.get_unsigned_bigint_byte_array(value, size);
        for(let i = 0; i <= size - 1; i++) {
            this.writeu8(byteArray[i]);
        }
        return byteArray;
    }

    write128_be(value) { // for bigint
        let size = 16; // bytes
        return this.write_unsigned_bigint_be(signed_to_unsigned_128(value, size), size);
    }

    writeu128_be(value) { // for bigint
        let size = 16; // bytes
        return this.write_unsigned_bigint_be(value, size);
    }

    write128_le(value) { // not used
        let size = 16;
        return this.write_unsigned_bigint_le(signed_to_unsigned_128(value, size), size);
    }

    writeu128_le(value) { // not used
        let size = 16;
        return this.write_unsigned_bigint_le(value, size);
    }
}


class Reader {
    /** 当前可以写入的缓冲区位置, `[0,  buffIndex)`的缓冲字节都已写满, 注意buffIndex处尚未写入. */
    buffIndex;
    /** ArrayBuffer */
    buffer;
    /** DataView for `this.buffer`. */
    dataView;
    /**
     * `this.textDecoder` will be initialized when necessary.
     * @type {TextDecoder} */
    textDecoder;

    /**
     * @param {ArrayBuffer} buffer
     */
    constructor(buffer) {
        this.buffer = buffer;
        this.dataView = new DataView(buffer);
        this.buffIndex = 0;
    }

    /** 返回当前索引字节的值, 无副作用. */
    peek() {
        return this.dataView.getUint8(this.buffIndex);
    }

    /**
     * 从缓冲区当前位置读取一个Int8.
     * 副作用: buffIndex增加1.
     * @returns {int}
     */
    dataview_read8() {
        let size = 1;
        let val = this.dataView.getInt8(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个UInt8.
     * 副作用: buffIndex增加1.
     * @returns {int}
     */
    dataview_readu8() {
        let size = 1;
        let val = this.dataView.getUint8(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个Int16.
     * 副作用: buffIndex增加2.
     * @returns {int}
     */
    dataview_read16() {
        let size = 2;
        let val = this.dataView.getInt16(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个UInt16.
     * 副作用: buffIndex增加2.
     * @returns {int}
     */
    dataview_readu16() {
        let size = 2;
        let val = this.dataView.getUint16(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个Int32.
     * 副作用: buffIndex增加4.
     * @returns {int}
     */
    dataview_read32() {
        let size = 4;
        let val = this.dataView.getInt32(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个UInt32.
     * 副作用: buffIndex增加4.
     * @returns {int}
     */
    dataview_readu32() {
        let size = 4;
        let val = this.dataView.getUint32(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个Int64.
     * 副作用: buffIndex增加8.
     * @returns {int | bigint}
     */
    dataview_read64() {
        let size = 8;
        let val = this.dataView.getBigInt64(this.buffIndex);
        this.buffIndex += size;
        if(val >= Number.MIN_SAFE_INTEGER && val <= Number.MAX_SAFE_INTEGER) {
            return Number(val);
        }
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个UInt64.
     * 副作用: buffIndex增加8.
     * @returns {int | bigint}
     */
    dataview_readu64() {
        let size = 8;
        let val = this.dataView.getBigUint64(this.buffIndex);
        this.buffIndex += size;
        if(val >= Number.MIN_SAFE_INTEGER && val <= Number.MAX_SAFE_INTEGER) {
            return Number(val);
        }
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个Float32.
     * 副作用: buffIndex增加4.
     * @returns {number}
     */
    dataview_read_single() {
        let size = 4;
        let float32 = this.dataView.getFloat32(this.buffIndex);
        this.buffIndex += size;
        return float32;
    }

    /**
     * 从缓冲区当前位置读取一个Float64.
     * 副作用: buffIndex增加8.
     * @returns {number}
     */
    dataview_read_double() {
        let size = 8;
        let float64 = this.dataView.getFloat64(this.buffIndex);
        this.buffIndex += size;
        return float64;
    }

    /**
     * 从缓冲区当前位置读取一个Int8.
     * 副作用: buffIndex增加1.
     * @returns {int}
     */
    read8() {
        let size = 1;
        // let val = this.dataView.getInt8(this.buffIndex); // 未测试
        let val = this.dataView.getInt8(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲区当前位置读取一个UInt8
     * 副作用: buffIndex增加1
     * @returns {int}
     */
    readu8() {
        let size = 1;
        let val = this.dataView.getUint8(this.buffIndex);
        this.buffIndex += size;
        return val;
    }

    /**
     * 从缓冲中读取一个无符号大端整数
     * 副作用: buffIndex增加size
     * @param {uint} size
     * @returns {int}
     */
    read_unsigned_be(size) {
        let val = 0n;
        for(let i = 0; i <= size - 1; i++) {
            val = (val << 8) + this.readu8();
        }
        return val;
    }

    read_unsigned_bigint_be(size) {
        let val = 0n;
        for(let i = 0; i <= size - 1; i++) {
            val = val * 256n + BigInt(this.readu8());
        }
        return val;
    }

    /**
     * 从缓冲中读取一个无符号小端整数
     * 副作用: buffIndex增加size
     * @param {uint} size
     * @returns {int}
     */
    read_unsigned_le(size) {
        let val = 0;
        for(let i = 0; i <= size - 1; i++) {
            val = val + (this.readu8() << (i * 8));
        }
        return val;
    }

    read_unsigned_bigint_le(size) {
        let val = 0n;
        for(let i = 0; i <= size - 1; i++) {
            //val = val + (this.readu8() << (i * 8));
            val = val + BigInt(this.readu8()) * bigint_pow(256n, BigInt(i));
        }
        return val;
    }

    read128_be() {
        let size = 16;
        return unsigned_to_signed_128(this.read_unsigned_bigint_be(size), size);
    }

    readu128_be() {
        let size = 16;
        return this.read_unsigned_bigint_be(size);
    }

    read128_le() { // not used
        let size = 16;
        return unsigned_to_signed_128(this.read_unsigned_bigint_le(size), size);
    }

    readu128_le() { // not used
        let size = 16;
        return this.read_unsigned_bigint_le(size);
    }
}

export {Writer, Reader};
