import * as Types from "./types";

export default class Headers {
    // headers
    static get BOOLEAN_HEADER   () { return 0b00000000; }  //b0000000n
    static get NUMBER_HEADER    () { return 0b00010000; }  //b0001nnnn
    static get CONTAINER_HEADER () { return 0b00100000; }  //b001xxfnn
    static get STRING_HEADER    () { return 0b01000000; }  //b010000nn
    static get REF_HEADER       () { return 0b01100000; }  //b011fdddd
    static get R_REF_HEADER     () { return 0b01100100; }  //b01100100
    static get POINTER_HEADER   () { return 0b01101000; }  //b011010nn
    static get TAG_HEADER       () { return 0b11100000; }  //b111fdddd
    static get CONS_HEADER      () { return 0b10000000; }  //b10000000
    static get PACKAGE_HEADER   () { return 0b10000001; }  //b10000001
    static get SYMBOL_HEADER    () { return 0b10000010; }  //b1000001f
    static get CHARACTER_HEADER () { return 0b10000100; }  //b100001nn
    static get PROPERTIES_HEADER() { return 0b10001000; }  //b10001000
    static get INDEX_HEADER     () { return 0b10100000; }  //b101fdddd
    // number header
    static get INT8        () { return 0x0; }
    static get INT16       () { return 0x1; }
    static get INT32       () { return 0x2; }
    static get INT64       () { return 0x3; }
    static get UINT8       () { return 0x4; }
    static get UINT16      () { return 0x5; }
    static get UINT32      () { return 0x6; }
    static get UINT64      () { return 0x7; }
    static get SINGLE_FLOAT() { return 0x8; }
    static get DOUBLE_FLOAT() { return 0x9; }
    static get INT128      () { return 0xA; }
    static get UINT128     () { return 0xB; }
    static get COMPLEX     () { return 0xC; }
    static get RATIONAL    () { return 0xF; }

    static get CONTAINER_VECTOR () { return 0b00100000; }
    static get CONTAINER_LIST   () { return 0b00101000; }
    static get CONTAINER_MAP    () { return 0b00110000; }
    static get CONTAINER_TMAP   () { return 0b00111000; }
    static get CONTAINER_FIXED  () { return 0b00000100; }

    static get REFTAG_INLINE    () { return 0b00010000; }
    static get SYMBOL_KEYWORD   () { return 0b00000001; }

    // Header predicating
    static boolean_p(n) {
        // (= 0 (ash n -1))) ; 负数表示右移
        return 0x0 === (n >> 1);
    }

    static number_p(n) {
        return 0b00010000 === (n & 0b11110000);
    }

    static container_p(n) {
        return 0b00100000 === (n & 0b11100000);
    }

    /**
     * 整数num的第N位为1则返回true, 否则返回false, N从0开始. Gets the Nth bit of m, N starts at 0
     * @param {integer} N
     * @param {integer} num
     * @returns {boolean}
     */
    static bit(N, num) {
        return num == 0 ? false : (((num & (1 << N)) >> N) === 1);
    }

    /**
     * Predicate returns true if bit index of int is 1.
     * @param {integer} index
     * @param {integer} int
     * @returns {boolean}
     */
    static logbitp(index, int) {
        return this.bit(index, int);
    }

    static container_fixed_p(n) {
        return this.logbitp(2, n);
    }

    static string_p(n) {
        return 0b01000000 === (n & 0b11111100);
    }

    static ref_p(n){
        return (0b01100000 === (n & 0b11111100)) || // id-follows
               (0b01110000 === (n & 0b11110000));   // id-inline
    }

    static remote_ref_p(n) {
        return this.R_REF_HEADER === n;
    }

    static pointer_header_p(n) {
        return this.POINTER_HEADER === (n & 0b111111100);
    }

    static tag_p(n) {
        return (0b11100000 === n & 0b11111100) ||  // id-follows
               (0b11110000 === n & 0b11110000);    // id-inline
    }

    static tag_inline_p(n) {
        return this.logbitp(4, n);
    }

    static cons_p(n) {
        return n === 0b10000000;
    }

    static package_p(n) {
        return n === 0b10000001;
    }

    static symbol_p(n) {
        return 0b10000010 === (n & 0b11111110);
    }

    static character_p(n) {
        return this.CHARACTER_HEADER === (n & 0b11111100);
    }

    static properties_p(n) {
        return this.PROPERTIES_HEADER === n;
    }

    static keyword_p(n) {
        return this.symbol_p(n) && this.logbitp(0, n);
    }

    static index_p(n) {
        return this.INDEX_HEADER === (this.INDEX_HEADER & n);
    }

    // Making headers
    static bits_size_type(bits) {
        switch(bits) {
            case 0x80: return 0b00;
            case 0x10: return 0b01;
            case 0x20: return 0b10;
            default: throw new Error(`bits values should be 8, 16, or 32 in bitsSizeType, but received ${bits}.`);
        }
    }

    static len_size_type(len) {
        if(len >= 0 && len <= 255)        { return 0b00; } // 2 ** 8  - 1
        if(len >= 0 && len <= 65535)      { return 0b01; } // 2 ** 16 - 1
        if(len >= 0 && len <= 4294967295) { return 0b10; } // 2 ** 32 - 1
        throw new Error(`lenSizeType out of range ${len}.`);
    }

    static size_type(header) {
        return 0b11 & header;
    }

    static size_bytes(header) {
        let type = this.size_type(header)
        if(0b00 === type) { return 1; }
        if(0b01 === type) { return 2; }
        if(0b10 === type) { return 4; }
        throw new Error(`invalid-header, value: ${header}, reason: invalid size-bytes.`);
    }

    /**
     *
     * @param {number | bigint} num
     * @returns {Symbol}
     */
    static guess_number_type(num) {
        // pyconspack/header.py guess_int(i), python版的guess_int函数的边界比较有bug
        if(Number.isInteger(num)) { // note that some integer may be greater than Number.MAX_SAFE_INTEGER
            if(num >= -128              && num <= 127)         { return Types.INT8;}    // [-(2**7),  2**7-1]
            else if(num >= 0            && num <= 255)         { return Types.UINT8; }  // [0,        2**8-1]
            else if(num >= -32768       && num <= 32767)       { return Types.INT16; }  // [-(2**15), 2**15-1]
            else if(num >= 0            && num <= 65535)       { return Types.UINT16; } // [0,        2**16-1]
            else if(num >= -2147483648  && num <= 2147483647)  { return Types.INT32; }  // [-(2**31), 2**31-1]
            else if(num >= 0            && num <= 4294967295)  { return Types.UINT32; } // [0,        2**32-1]
            else if(num >= -9223372036854775808n  && num <= 9223372036854775807n)  { return Types.INT64; }
            else if(num >= 0n                     && num <= 18446744073709551615n) { return Types.UINT64; }
            else if(num ===  Number.MAX_VALUE) { return Types.DOUBLE_FLOAT; } // special number
            else if(num === -Number.MAX_VALUE) { return Types.DOUBLE_FLOAT; }
            else { throw new Error(`Integer ${num} is out of range!`); }
        }
        else {
            if(Number.isFinite(num)) { // for finite Number type
                return Types.DOUBLE_FLOAT;
            }
            else if (typeof(num) === 'bigint') {
                if(num >= -9223372036854775808n && num <= 9223372036854775807n)  { return Types.INT64; }
                else if(num >= 0n               && num <= 18446744073709551615n) { return Types.UINT64; }
                else if(num >= -170141183460469231731687303715884105728n && num <= 170141183460469231731687303715884105727n)  { return Types.INT128; }
                else if(num >= 0n                                        && num <= 340282366920938463463374607431768211455n)  { return Types.UINT128; }
                throw new Error(`Number ${num} is larger than 16 bytes and it is not supported!`);
            }
            else {
                throw new Error(`Cannot encode this number ${num}.`);
            }
        }
    }

    /**
     *
     * @param {Symbol} type
     * @returns
     */
    static number_header(type) {
        switch(type) {
            case Types.INT8        : return this.NUMBER_HEADER | 0x0;
            case Types.INT16       : return this.NUMBER_HEADER | 0x1;
            case Types.INT32       : return this.NUMBER_HEADER | 0x2;
            case Types.INT64       : return this.NUMBER_HEADER | 0x3;
            case Types.UINT8       : return this.NUMBER_HEADER | 0x4;
            case Types.UINT16      : return this.NUMBER_HEADER | 0x5;
            case Types.UINT32      : return this.NUMBER_HEADER | 0x6;
            case Types.UINT64      : return this.NUMBER_HEADER | 0x7;
            case Types.SINGLE_FLOAT: return this.NUMBER_HEADER | 0x8;
            case Types.DOUBLE_FLOAT: return this.NUMBER_HEADER | 0x9;
            case Types.INT128      : return this.NUMBER_HEADER | 0xA;
            case Types.UINT128     : return this.NUMBER_HEADER | 0xB;
            case Types.COMPLEX     : throw new Error(`Type ${type} is not supported!`);
            case Types.RATIONAL    : throw new Error(`Type ${type} is not supported!`);
            default: throw new Error(`The header cannot recognize this type ${type}.`);
        }
    }

    /**
     *
     * @param {Symbol} type
     * @returns {integer}
     */
    static container_type(type) {
        switch(type) {
            case Types.VECTOR: return this.CONTAINER_VECTOR;
            case Types.LIST:   return this.CONTAINER_LIST;
            case Types.MAP:    return this.CONTAINER_MAP;
            case Types.TMAP:   return this.CONTAINER_TMAP;
            default: throw new Error(`Type ${type} is not supported.`);
        }
    }

    /**
     *
     * @param {Symbol} type
     * @param {boolean} fixed_p
     * @returns {uint8}
     */
    static container_header(type, fixed_p) {
        let type_bits = this.container_type(type);
        let fixed_bits =  fixed_p ? this.CONTAINER_FIXED : 0x0;
        return type_bits | fixed_bits;
    }

    /**
     *
     * @param {Symbol} value
     * @param {boolean} moduleName
     * @returns {boolean}
     */
    static keywordp(value, moduleName = false) {
        // julia's symbols are not belong to any module, and they are more like keywords in lisp.
        // to get compatible with lisp cl-conspack,
        // all symbols which cannot getfield in the spefified module are treated as keywords.
        // return true;
        // js has no keyword type
        throw new Error(`Keyword type is not supported: ${value}.`);
    }

    static keyword_header() {
        //throw new Error("Keyword type is not supported");
        return this.SYMBOL_HEADER | this.SYMBOL_KEYWORD;
    }

    static symbol_header() {
        return this.SYMBOL_HEADER;
    }

    static character_header(c) {
        throw new Error(`Javascript has no character type: ${c}.`);
        //return this.CHARACTER_HEADER | ncodeunits(c);
    }


    // Decode
    /**
     *
     * @param {uint8} header_code
     * @returns {Symbol}
     */
    static decode_header(header_code) {
        if(this.number_p(header_code))         { return Types.NUMBER ; }
        if(this.string_p(header_code))         { return Types.STRING ; }
        if(this.container_p(header_code))      { return Types.CONTAINER; }
        if(this.cons_p(header_code))           { return Types.CONS; }
        if(this.properties_p(header_code))     { return Types.PROPERTIES; }
        if(this.boolean_p(header_code))        { return Types.BOOLEAN; }
        if(this.symbol_p(header_code))         { return Types.SYMBOL; }
        if(this.character_p(header_code))      { return Types.CHARACTER; }
        if(this.package_p(header_code))        { return Types.PACKAGE; }
        throw Error(`invalid-header ${header_code}, reason: 'unknown or not supported type'.`);
        if(this.index_p(header_code))          { return Types.INDEX; }
        if(this.ref_p(header_code))            { return Types.REF; }
        if(this.remote_ref_p(header_code))     { return Types.R_REF; }
        if(this.pointer_header_p(header_code)) { return Types.POINTER; }
        if(this.tag_p(header_code))            { return Types.TAG; }
    }

    /**
     *
     * @param {uint8} header_code
     * @returns {Symbol}
     */
    static decode_number_header(header_code) {
        switch(0x0F & header_code) {
            case 0x0: return Types.INT8;         // :int8
            case 0x1: return Types.INT16;        // :int16
            case 0x2: return Types.INT32;        // :int32
            case 0x3: return Types.INT64;        // :int64
            case 0x4: return Types.UINT8;        // :uint8
            case 0x5: return Types.UINT16;       // :uint16
            case 0x6: return Types.UINT32;       // :uint32
            case 0x7: return Types.UINT64;       // :uint64
            case 0x8: return Types.SINGLE_FLOAT; // :single_float
            case 0x9: return Types.DOUBLE_FLOAT; // :double_float
            case 0xA: return Types.INT128;       // :int128
            case 0xB: return Types.UINT128;      // :uint128
            case 0xC: return Types.COMPLEX;      // :complex
            case 0xF: return Types.RATIONAL;     // :rational
            default: throw new Error(`invalid header :value ${header_code}, :reason 'reserved number type'.`);
        }
    }

    /**
     *
     * @param {integer} sizeCode
     */
    static number_size(sizeCode) {
        switch (sizeCode & 0x0F) {
            case 0x0: return 1;
            case 0x1: return 2;
            case 0x2: return 4;
            case 0x3: return 8;
            case 0x4: return 1;
            case 0x5: return 2;
            case 0x6: return 4;
            case 0x7: return 8;
            case 0x8: return 4;
            case 0x9: return 8;
            case 0xA: return 16;
            case 0xB: return 16;
            case 0xC: return 16;
            // more of an estimate
            case 0xF: return 16;
            default: throw new Error(`invalid-header, value ${sizeCode}, reason: reserved number type.`);
        }
    }

    /**
     *
     * @param {uint8} header_code
     * @returns {Symbol}
     */
    static decode_container_type(header_code) {
        let ldb_2_to_3 = (header_code & 0b11111) >> 3;
        switch(ldb_2_to_3) {
            case 0b00: return Types.VECTOR;  // :vector
            case 0b01: return Types.LIST;    // :list
            case 0b10: return Types.MAP;     // :map
            case 0b11: return Types.TMAP;    // :tmap
            default: throw Error(`Header code ${header_code} leads to an invalid ldb result ${ldb_2_to_3}.`);
        }
    }
}
