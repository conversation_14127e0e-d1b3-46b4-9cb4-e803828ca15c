// src/core/assets/AssetLoaders.js
// 资源加载器模块 - 负责不同类型资源的具体加载逻辑

import { 
    SceneLoader,
    Texture,
    Sound,
    Tools
} from '@babylonjs/core';
import { AssetType } from './AssetManager.js';

/**
 * 资源加载器类
 * 提供不同类型资源的加载方法
 */
export class AssetLoaders {
    constructor(assetManager) {
        this.assetManager = assetManager;
        
        // 加载器映射
        this.loaderMap = new Map([
            [AssetType.MODEL, this.loadModel.bind(this)],
            [AssetType.TEXTURE, this.loadTexture.bind(this)],
            [AssetType.SOUND, this.loadSound.bind(this)],
            [AssetType.CONFIG, this.loadConfig.bind(this)]
        ]);

        console.log('资源加载器已初始化');
    }

    /**
     * 通用加载方法
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {Object} options - 加载选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 加载的资源
     */
    async load(url, type, options = {}, onProgress = null) {
        const loader = this.loaderMap.get(type);
        
        if (!loader) {
            throw new Error(`不支持的资源类型: ${type}`);
        }

        return loader(url, options, onProgress);
    }

    /**
     * 加载3D模型
     * @param {string} url - 模型文件URL
     * @param {Object} options - 加载选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 模型资源对象
     */
    async loadModel(url, options = {}, onProgress = null) {
        const {
            scene = null, // Babylon.js场景对象
            importMeshes = null, // 要导入的网格名称数组
            scaleFactor = 1, // 缩放因子
            position = null, // 位置
            rotation = null, // 旋转
            enableCollisions = false, // 启用碰撞
            enablePhysics = false, // 启用物理
            ...babylonOptions
        } = options;

        if (!scene) {
            throw new Error('加载模型需要提供Babylon.js场景对象');
        }

        try {
            console.log(`开始加载3D模型: ${url}`);

            // 使用Babylon.js SceneLoader加载模型
            const result = await new Promise((resolve, reject) => {
                SceneLoader.ImportMesh(
                    importMeshes || "", // 空字符串表示导入所有网格
                    "", // 根路径
                    url, // 文件名或完整URL
                    scene,
                    (meshes, particleSystems, skeletons, animationGroups) => {
                        resolve({
                            meshes,
                            particleSystems,
                            skeletons,
                            animationGroups,
                            url,
                            type: AssetType.MODEL
                        });
                    },
                    (progress) => {
                        if (onProgress) {
                            const progressPercent = progress.lengthComputable 
                                ? (progress.loaded / progress.total) * 100 
                                : 0;
                            onProgress(progressPercent);
                        }
                    },
                    (scene, message, exception) => {
                        reject(new Error(`模型加载失败: ${message || exception}`));
                    }
                );
            });

            // 后处理：应用变换和设置
            if (result.meshes && result.meshes.length > 0) {
                result.meshes.forEach(mesh => {
                    // 应用缩放
                    if (scaleFactor !== 1) {
                        mesh.scaling.scaleInPlace(scaleFactor);
                    }

                    // 设置位置
                    if (position) {
                        mesh.position.copyFrom(position);
                    }

                    // 设置旋转
                    if (rotation) {
                        mesh.rotation.copyFrom(rotation);
                    }

                    // 启用碰撞
                    if (enableCollisions) {
                        mesh.checkCollisions = true;
                    }

                    // 启用物理（需要物理引擎）
                    if (enablePhysics && scene.getPhysicsEngine()) {
                        mesh.physicsImpostor = new PhysicsImpostor(
                            mesh, 
                            PhysicsImpostor.BoxImpostor, 
                            { mass: 1, restitution: 0.7 }
                        );
                    }
                });
            }

            // 添加dispose方法用于资源清理
            result.dispose = () => {
                if (result.meshes) {
                    result.meshes.forEach(mesh => mesh.dispose());
                }
                if (result.particleSystems) {
                    result.particleSystems.forEach(ps => ps.dispose());
                }
                if (result.skeletons) {
                    result.skeletons.forEach(skeleton => skeleton.dispose());
                }
                if (result.animationGroups) {
                    result.animationGroups.forEach(ag => ag.dispose());
                }
            };

            console.log(`3D模型加载完成: ${url}, 网格数量: ${result.meshes.length}`);
            return result;

        } catch (error) {
            console.error(`3D模型加载失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 加载纹理
     * @param {string} url - 纹理文件URL
     * @param {Object} options - 加载选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 纹理资源对象
     */
    async loadTexture(url, options = {}, onProgress = null) {
        const {
            scene = null, // Babylon.js场景对象
            noMipmap = false, // 不生成mipmap
            invertY = true, // 反转Y轴
            samplingMode = Texture.TRILINEAR_SAMPLINGMODE, // 采样模式
            format = null, // 纹理格式
            ...babylonOptions
        } = options;

        if (!scene) {
            throw new Error('加载纹理需要提供Babylon.js场景对象');
        }

        try {
            console.log(`开始加载纹理: ${url}`);

            const texture = await new Promise((resolve, reject) => {
                const tex = new Texture(
                    url,
                    scene,
                    noMipmap,
                    invertY,
                    samplingMode,
                    () => {
                        // 加载成功回调
                        resolve(tex);
                    },
                    (message, exception) => {
                        // 加载失败回调
                        reject(new Error(`纹理加载失败: ${message || exception}`));
                    },
                    null, // buffer
                    false, // deleteBuffer
                    format
                );

                // 模拟进度（纹理加载通常没有详细进度信息）
                if (onProgress) {
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 10;
                        if (progress >= 100) {
                            clearInterval(progressInterval);
                            return;
                        }
                        onProgress(progress);
                    }, 50);
                }
            });

            // 创建纹理资源对象
            const textureAsset = {
                texture,
                url,
                type: AssetType.TEXTURE,
                width: texture.getSize().width,
                height: texture.getSize().height,
                format: texture.format,
                dispose: () => texture.dispose()
            };

            console.log(`纹理加载完成: ${url}, 尺寸: ${textureAsset.width}x${textureAsset.height}`);
            return textureAsset;

        } catch (error) {
            console.error(`纹理加载失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 加载音频
     * @param {string} url - 音频文件URL
     * @param {Object} options - 加载选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 音频资源对象
     */
    async loadSound(url, options = {}, onProgress = null) {
        const {
            scene = null, // Babylon.js场景对象
            autoplay = false, // 自动播放
            loop = false, // 循环播放
            volume = 1.0, // 音量
            spatialSound = false, // 空间音效
            maxDistance = 100, // 最大距离（空间音效）
            ...babylonOptions
        } = options;

        try {
            console.log(`开始加载音频: ${url}`);

            const sound = await new Promise((resolve, reject) => {
                const audio = new Sound(
                    url, // 名称
                    url, // URL
                    scene,
                    () => {
                        // 加载成功回调
                        resolve(audio);
                    },
                    {
                        autoplay,
                        loop,
                        volume,
                        spatialSound,
                        maxDistance,
                        ...babylonOptions
                    }
                );

                // 设置错误处理
                audio.onError = (error) => {
                    reject(new Error(`音频加载失败: ${error}`));
                };

                // 模拟进度
                if (onProgress) {
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 20;
                        if (progress >= 100) {
                            clearInterval(progressInterval);
                            return;
                        }
                        onProgress(progress);
                    }, 100);
                }
            });

            // 创建音频资源对象
            const soundAsset = {
                sound,
                url,
                type: AssetType.SOUND,
                duration: sound.getLength(),
                isPlaying: sound.isPlaying,
                volume: sound.getVolume(),
                // 音频控制方法
                play: () => sound.play(),
                pause: () => sound.pause(),
                stop: () => sound.stop(),
                setVolume: (vol) => sound.setVolume(vol),
                dispose: () => sound.dispose()
            };

            console.log(`音频加载完成: ${url}, 时长: ${soundAsset.duration}秒`);
            return soundAsset;

        } catch (error) {
            console.error(`音频加载失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 加载配置文件
     * @param {string} url - 配置文件URL
     * @param {Object} options - 加载选项
     * @param {Function} onProgress - 进度回调
     * @returns {Promise<Object>} 配置数据对象
     */
    async loadConfig(url, options = {}, onProgress = null) {
        const {
            format = 'auto', // 格式：auto, json, xml
            encoding = 'utf-8', // 编码
            timeout = 30000, // 超时时间（毫秒）
            ...fetchOptions
        } = options;

        try {
            console.log(`开始加载配置文件: ${url}`);

            // 模拟进度
            if (onProgress) {
                onProgress(10);
            }

            // 使用fetch加载文件
            const response = await Promise.race([
                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...fetchOptions.headers
                    },
                    ...fetchOptions
                }),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('请求超时')), timeout)
                )
            ]);

            if (onProgress) {
                onProgress(50);
            }

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }

            // 根据格式解析数据
            let data;
            const detectedFormat = format === 'auto' ? this._detectConfigFormat(url) : format;

            if (onProgress) {
                onProgress(80);
            }

            switch (detectedFormat) {
                case 'json':
                    data = await response.json();
                    break;
                case 'xml':
                    const xmlText = await response.text();
                    data = this._parseXML(xmlText);
                    break;
                default:
                    data = await response.text();
            }

            if (onProgress) {
                onProgress(100);
            }

            // 创建配置资源对象
            const configAsset = {
                data,
                url,
                type: AssetType.CONFIG,
                format: detectedFormat,
                size: JSON.stringify(data).length,
                dispose: () => {
                    // 配置文件通常不需要特殊清理
                }
            };

            console.log(`配置文件加载完成: ${url}, 格式: ${detectedFormat}, 大小: ${configAsset.size}字节`);
            return configAsset;

        } catch (error) {
            console.error(`配置文件加载失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 检测配置文件格式
     * @private
     */
    _detectConfigFormat(url) {
        const extension = url.split('.').pop().toLowerCase();
        
        switch (extension) {
            case 'json':
                return 'json';
            case 'xml':
                return 'xml';
            default:
                return 'text';
        }
    }

    /**
     * 解析XML文本
     * @private
     */
    _parseXML(xmlText) {
        try {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
            
            // 检查解析错误
            const parseError = xmlDoc.getElementsByTagName('parsererror');
            if (parseError.length > 0) {
                throw new Error('XML解析错误');
            }

            // 将XML转换为JavaScript对象（简化版本）
            return this._xmlToObject(xmlDoc.documentElement);
        } catch (error) {
            console.error('XML解析失败:', error);
            throw error;
        }
    }

    /**
     * 将XML节点转换为JavaScript对象
     * @private
     */
    _xmlToObject(node) {
        const obj = {};
        
        // 处理属性
        if (node.attributes) {
            for (let i = 0; i < node.attributes.length; i++) {
                const attr = node.attributes[i];
                obj[`@${attr.name}`] = attr.value;
            }
        }

        // 处理子节点
        if (node.childNodes) {
            for (let i = 0; i < node.childNodes.length; i++) {
                const child = node.childNodes[i];
                
                if (child.nodeType === Node.TEXT_NODE) {
                    const text = child.textContent.trim();
                    if (text) {
                        obj['#text'] = text;
                    }
                } else if (child.nodeType === Node.ELEMENT_NODE) {
                    const childObj = this._xmlToObject(child);
                    
                    if (obj[child.nodeName]) {
                        // 如果已存在同名节点，转换为数组
                        if (!Array.isArray(obj[child.nodeName])) {
                            obj[child.nodeName] = [obj[child.nodeName]];
                        }
                        obj[child.nodeName].push(childObj);
                    } else {
                        obj[child.nodeName] = childObj;
                    }
                }
            }
        }

        return obj;
    }

    /**
     * 注册自定义加载器
     * @param {string} type - 资源类型
     * @param {Function} loader - 加载器函数
     */
    registerLoader(type, loader) {
        this.loaderMap.set(type, loader);
        console.log(`已注册自定义加载器: ${type}`);
    }

    /**
     * 获取支持的资源类型
     * @returns {Array<string>} 支持的资源类型列表
     */
    getSupportedTypes() {
        return Array.from(this.loaderMap.keys());
    }
}

export default AssetLoaders;
