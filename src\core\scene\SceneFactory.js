// src/core/SceneFactory.js
// 场景工厂 - 使用工厂模式创建不同类型的场景实例

// 导入所有可用的场景类
import { DefaultSceneWithTexture } from "../../scenes/defaultWithTexture.js";
import { FresnelShaderScene } from "../../scenes/fresnelShader.js";
import { LoadModelAndEnvScene } from "../../scenes/loadModelAndEnv.js";
import { NavigationMeshRecast } from "../../scenes/navigationMeshRecast.js";
import { PhysicsSceneWithAmmo } from "../../scenes/physicsWithAmmo.js";
import { PhysicsSceneWithHavok } from "../../scenes/physicsWithHavok.js";
import { ShanhaijingWorldScene } from "../../scenes/shanhaijingWorld.js";

/**
 * 场景工厂类
 * 负责根据场景类型创建对应的场景实例
 */
export class SceneFactory {
    constructor() {
        // 场景类型映射表,
        // key是DefaultSceneConfigs的value的type数据, value是对象{name, description, factory, category}, 其中factory是创建场景实例的函数.
        // 相同type, 如果给factory传入不同的options, 就可以得到不同风格的场景(例如天气, 时间, 环境等), 因此命名为type而不是id,
        // 为了区分同一type下的不同实例, 因此需要一个id属性, 这个id是SceneManager中scenes的key.
        this.sceneTypes = new Map();

        // 注册所有内置场景类型
        this.registerBuiltinScenes();

        console.log("场景工厂已初始化");
    }

    /**
     * 向sceneTypes注册内置场景类型, registerSceneType则注册任意的场景类型.
     * @private
     */
    registerBuiltinScenes() {
        // 注册默认纹理场景
        this.sceneTypes.set('default', {
            name: '默认纹理场景',
            description: '基础的Babylon.js场景，包含球体和纹理地面',
            factory: (options) => new DefaultSceneWithTexture(),
            category: 'basic'
        });

        // 注册菲涅尔着色器场景
        this.sceneTypes.set('fresnel', {
            name: '菲涅尔着色器场景',
            description: '展示菲涅尔着色器效果的场景',
            factory: (options) => new FresnelShaderScene(),
            category: 'shader'
        });

        // 注册模型和环境加载场景
        this.sceneTypes.set('modelAndEnv', {
            name: '模型环境场景',
            description: '加载3D模型和环境的场景',
            factory: (options) => new LoadModelAndEnvScene(),
            category: 'model'
        });

        // 注册导航网格场景
        this.sceneTypes.set('navigation', {
            name: '导航网格场景',
            description: '使用Recast导航网格的场景',
            factory: (options) => new NavigationMeshRecast(),
            category: 'navigation'
        });

        // 注册Ammo物理场景
        this.sceneTypes.set('physicsAmmo', {
            name: 'Ammo物理场景',
            description: '使用Ammo.js物理引擎的场景',
            factory: (options) => new PhysicsSceneWithAmmo(),
            category: 'physics'
        });

        // 注册Havok物理场景
        this.sceneTypes.set('physicsHavok', {
            name: 'Havok物理场景',
            description: '使用Havok物理引擎的场景',
            factory: (options) => new PhysicsSceneWithHavok(),
            category: 'physics'
        });

        // 注册山海经世界场景（默认主场景）
        this.sceneTypes.set('shanhaijing', {
            name: '山海经世界场景',
            description: '完整的山海经主题MMORPG场景，包含地形、环境、玩家系统',
            factory: (options) => new ShanhaijingWorldScene(),
            category: 'game'
        });

        console.log(`已注册 ${this.sceneTypes.size} 个内置场景类型`);
    }

    /**
     * 创建场景实例
     * @param {string} type - 场景类型
     * @param {Object} options - 场景选项
     * @returns {Object} 场景实例
     */
    createScene(type, options = {}) {
        const sceneInfo = this.sceneTypes.get(type);

        if (!sceneInfo) {
            throw new Error(`未知的场景类型: ${type}`);
        }

        console.log(`创建场景实例: ${sceneInfo.name} (${type})`);

        try {
            // 使用工厂函数创建场景实例
            const sceneInstance = sceneInfo.factory(options);

            // 添加场景元数据
            sceneInstance._sceneType = type;
            sceneInstance._sceneName = sceneInfo.name;
            sceneInstance._sceneDescription = sceneInfo.description;
            sceneInstance._sceneCategory = sceneInfo.category;
            sceneInstance._createdAt = Date.now();

            return sceneInstance;

        } catch (error) {
            console.error(`场景实例创建失败: ${type}`, error);
            throw error;
        }
    }

    /**
     * 注册自定义场景类型, registerBuiltinScenes则注册内置场景类型.
     * @param {string} type - 场景类型标识符
     * @param {Object} sceneInfo - 场景信息对象
     * @param {string} sceneInfo.name - 场景名称
     * @param {string} sceneInfo.description - 场景描述
     * @param {Function} sceneInfo.factory - 场景工厂函数
     * @param {string} sceneInfo.category - 场景分类
     */
    registerSceneType(type, sceneInfo) {
        if (this.sceneTypes.has(type)) {
            console.warn(`场景类型 ${type} 已存在，将被覆盖`);
        }

        if (typeof sceneInfo.factory !== 'function') {
            throw new Error(`场景工厂必须是函数: ${type}`);
        }

        this.sceneTypes.set(type, {
            name: sceneInfo.name || type,
            description: sceneInfo.description || '',
            factory: sceneInfo.factory,
            category: sceneInfo.category || 'custom'
        });

        console.log(`自定义场景类型已注册: ${type}`);
    }

    /**
     * 从sceneTypes获取所有可用的场景类型
     * 注意, 这里的type实际上是`registerBuiltinScenes`中场景的id, 即sceneTypes的key
     * @returns {Array} 场景类型信息数组
     */
    getAvailableSceneTypes() {
        return Array.from(this.sceneTypes.entries()).map(([type, info]) => ({
            type,
            name: info.name,
            description: info.description,
            category: info.category
        }));
    }

    /**
     * 获取指定分类的场景类型
     * @param {string} category - 场景分类
     * @returns {Array} 场景类型信息数组
     */
    getSceneTypesByCategory(category) {
        return this.getAvailableSceneTypes().filter(scene => scene.category === category);
    }

    /**
     * 检查场景类型是否存在
     * @param {string} type - 场景类型
     * @returns {boolean} 是否存在
     */
    hasSceneType(type) {
        return this.sceneTypes.has(type);
    }

    /**
     * 获取场景类型信息
     * @param {string} type - 场景类型
     * @returns {Object|null} 场景类型信息
     */
    getSceneTypeInfo(type) {
        const sceneInfo = this.sceneTypes.get(type);
        if (!sceneInfo) {
            return null;
        }

        return {
            type,
            name: sceneInfo.name,
            description: sceneInfo.description,
            category: sceneInfo.category
        };
    }

    /**
     * 移除场景类型
     * @param {string} type - 场景类型
     */
    unregisterSceneType(type) {
        if (this.sceneTypes.has(type)) {
            this.sceneTypes.delete(type);
            console.log(`场景类型已移除: ${type}`);
        } else {
            console.warn(`场景类型不存在，无法移除: ${type}`);
        }
    }

    /**
     * 获取所有场景分类
     * @returns {Array<string>} 分类数组
     */
    getCategories() {
        const categories = new Set();
        this.sceneTypes.forEach(info => {
            categories.add(info.category);
        });
        return Array.from(categories);
    }

    /**
     * 清理工厂
     */
    dispose() {
        this.sceneTypes.clear();
        console.log("场景工厂已清理");
    }
}

// 创建全局场景工厂实例
const sceneFactory = new SceneFactory();

export default sceneFactory;
