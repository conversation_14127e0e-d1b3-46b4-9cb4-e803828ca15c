# 山海经世界快速开始指南

## 🎮 游戏控制

### 基础操作
- **移动**: WASD键
- **跳跃**: 空格键
- **视角控制**: 鼠标拖拽旋转视角
- **缩放**: 鼠标滚轮

### 高级操作
- **F12**: 打开浏览器开发者工具（用于调试）

## 🌍 世界特色

### 地形系统
- **程序化生成**: 每次启动都会生成独特的地形
- **物理交互**: 真实的地形碰撞和物理效果
- **多层材质**: 基于高度的材质变化（草地→岩石→雪地）

### 环境系统
- **昼夜循环**: 10分钟完整的昼夜循环
- **动态光照**: 太阳位置和光照颜色随时间变化
- **天气系统**: 支持多种天气效果
- **雾效**: 动态雾效增强氛围

## 🛠️ 调试功能

在浏览器控制台中，你可以使用以下命令：

### 时间控制
```javascript
// 设置为正午
window.sceneModule.setTimeOfDay(0.5);

// 设置为午夜
window.sceneModule.setTimeOfDay(0.0);

// 设置为黄昏
window.sceneModule.setTimeOfDay(0.75);
```

### 天气控制
```javascript
// 晴天
window.sceneModule.setWeather('clear');

// 多云
window.sceneModule.setWeather('cloudy');

// 雨天
window.sceneModule.setWeather('lightRain');

// 雾天
window.sceneModule.setWeather('fog');
```

### 查看状态
```javascript
// 查看场景信息
console.log(window.sceneModule.getSceneInfo());

// 查看环境信息
console.log(window.environmentManager.getTimeInfo());
```

### 地形重新生成
```javascript
// 生成新的随机地形
window.terrainGenerator.generateTerrain('mountains', Math.random() * 100000);
```

## 🎯 体验建议

### 第一次游玩
1. **观察地形**: 注意地形的自然起伏和材质变化
2. **体验物理**: 尝试在不同坡度上移动和跳跃
3. **观察光照**: 等待或手动调整时间，观察光照变化
4. **测试天气**: 切换不同天气，感受氛围变化

### 探索要点
- 🏔️ **地形多样性**: 寻找高山、平原、峡谷等不同地形
- 🌅 **光影效果**: 在不同时间观察光影变化
- 🌦️ **天气体验**: 体验不同天气下的视觉效果
- 🎮 **物理交互**: 测试跳跃、移动的物理反馈

## 🔧 技术信息

### 性能要求
- **浏览器**: 支持WebGL 2.0的现代浏览器
- **内存**: 建议4GB以上
- **显卡**: 支持硬件加速的显卡

### 支持的浏览器
- Chrome 80+
- Firefox 75+
- Safari 14+
- Edge 80+

### WebGPU支持
如果你的浏览器支持WebGPU，可以通过以下URL启用：
```
http://localhost:8081/?engine=webgpu
```

## 🐛 常见问题

### 性能问题
- **降低画质**: 关闭浏览器的其他标签页
- **检查硬件**: 确保显卡驱动程序是最新的
- **内存不足**: 刷新页面重新加载

### 显示问题
- **黑屏**: 检查浏览器控制台是否有错误
- **地形不显示**: 等待地形生成完成
- **光照异常**: 尝试调整时间设置

### 控制问题
- **移动不响应**: 确保页面获得了焦点
- **相机卡顿**: 检查鼠标是否正常工作
- **跳跃失效**: 确保玩家在地面上

## 🎨 山海经主题元素

### 文化特色
- **古代时辰**: 时间系统基于古代十二时辰
- **五行元素**: 环境效果融入五行理念
- **神秘氛围**: 光效和雾效营造神秘感

### 未来内容
- 🐉 **神兽系统**: 各种山海经神兽
- 🏛️ **古建筑**: 中式古典建筑
- 🌸 **植被系统**: 古代植物和花卉
- 💫 **特效系统**: 仙气、灵光等特效

## 📝 反馈和建议

如果你在体验过程中发现问题或有改进建议，请：
1. 记录浏览器控制台的错误信息
2. 描述具体的问题场景
3. 提供你的浏览器和系统信息

## 🚀 下一步

这只是山海经世界的第一个版本，后续将会添加：
- 更丰富的地形类型
- 神兽和NPC系统
- 任务和剧情系统
- 多人在线功能
- 更多山海经文化元素

享受你的山海经世界探索之旅！🏔️✨
