/**
 * 物理工具类 (PhysicsUtils)
 * 提供物理系统相关的实用工具函数
 * 简化物理体创建和ECS集成
 */

import { Vector3, Quaternion } from "@babylonjs/core/Maths/math.vector";
import { MeshBuilder } from "@babylonjs/core/Meshes/meshBuilder";
import { StandardMaterial } from "@babylonjs/core/Materials/standardMaterial";
import { Color3 } from "@babylonjs/core/Maths/math.color";
import { PhysicsMaterialPresets, PhysicsShapeTypes } from './PhysicsManager.js';
import { Transform, Physics } from '../ecs/bitECSSystem.js';

/**
 * 物理体预设配置
 */
export const PhysicsBodyPresets = {
    // 玩家角色
    PLAYER: {
        mass: 70,
        friction: 0.8,
        restitution: 0.1,
        shapeType: PhysicsShapeTypes.CAPSULE,
        shapeOptions: {
            pointA: new Vector3(0, -0.9, 0),
            pointB: new Vector3(0, 0.9, 0),
            radius: 0.4
        }
    },
    
    // 普通箱子
    BOX: {
        mass: 10,
        friction: 0.6,
        restitution: 0.3,
        shapeType: PhysicsShapeTypes.BOX,
        shapeOptions: {
            size: new Vector3(1, 1, 1)
        }
    },
    
    // 球体
    BALL: {
        mass: 5,
        friction: 0.4,
        restitution: 0.8,
        shapeType: PhysicsShapeTypes.SPHERE,
        shapeOptions: {
            radius: 0.5
        }
    },
    
    // 静态地面
    GROUND: {
        mass: 0,
        friction: 0.7,
        restitution: 0.2,
        motionType: 'STATIC',
        shapeType: PhysicsShapeTypes.BOX,
        shapeOptions: {
            size: new Vector3(50, 0.1, 50)
        }
    },
    
    // 触发器区域
    TRIGGER: {
        mass: 0,
        friction: 0,
        restitution: 0,
        motionType: 'STATIC',
        shapeType: PhysicsShapeTypes.BOX,
        isTrigger: true
    }
};

/**
 * 物理工具类
 */
export class PhysicsUtils {
    /**
     * 为ECS实体创建完整的物理对象（网格+物理体）
     * @param {Object} ecsWorld - ECS世界实例
     * @param {PhysicsManager} physicsManager - 物理管理器
     * @param {Scene} scene - Babylon.js场景
     * @param {Object} options - 创建选项
     * @returns {Object} 包含entity、mesh、physicsBody的对象
     */
    static createPhysicsEntity(ecsWorld, physicsManager, scene, options = {}) {
        const {
            name = 'PhysicsEntity',
            position = Vector3.Zero(),
            rotation = Vector3.Zero(),
            scale = Vector3.One(),
            preset = 'BOX',
            meshType = 'box',
            meshOptions = {},
            materialColor = Color3.Gray(),
            customPhysicsOptions = {}
        } = options;

        try {
            // 获取物理预设
            const physicsPreset = PhysicsBodyPresets[preset] || PhysicsBodyPresets.BOX;
            const finalPhysicsOptions = { ...physicsPreset, ...customPhysicsOptions };

            // 创建网格
            let mesh;
            switch (meshType) {
                case 'box':
                    mesh = MeshBuilder.CreateBox(name, { 
                        size: 1, 
                        ...meshOptions 
                    }, scene);
                    break;
                case 'sphere':
                    mesh = MeshBuilder.CreateSphere(name, { 
                        diameter: 1, 
                        ...meshOptions 
                    }, scene);
                    break;
                case 'ground':
                    mesh = MeshBuilder.CreateGround(name, { 
                        width: 10, 
                        height: 10, 
                        ...meshOptions 
                    }, scene);
                    break;
                case 'capsule':
                    mesh = MeshBuilder.CreateCapsule(name, { 
                        height: 2, 
                        radius: 0.5, 
                        ...meshOptions 
                    }, scene);
                    break;
                default:
                    mesh = MeshBuilder.CreateBox(name, { size: 1 }, scene);
            }

            // 设置网格变换
            mesh.position = position;
            mesh.rotation = rotation;
            mesh.scaling = scale;

            // 创建材质
            const material = new StandardMaterial(name + '_material', scene);
            material.diffuseColor = materialColor;
            mesh.material = material;

            // 创建ECS实体
            const entity = ecsWorld.createEntity();

            // 添加Transform组件
            ecsWorld.addComponent(entity, Transform, {
                x: position.x,
                y: position.y,
                z: position.z,
                rotationX: rotation.x,
                rotationY: rotation.y,
                rotationZ: rotation.z,
                scaleX: scale.x,
                scaleY: scale.y,
                scaleZ: scale.z
            });

            // 添加Physics组件
            ecsWorld.addComponent(entity, Physics, {
                mass: finalPhysicsOptions.mass,
                velocityX: 0,
                velocityY: 0,
                velocityZ: 0,
                friction: finalPhysicsOptions.friction,
                restitution: finalPhysicsOptions.restitution,
                isKinematic: finalPhysicsOptions.motionType === 'KINEMATIC' ? 1 : 0,
                isStatic: finalPhysicsOptions.motionType === 'STATIC' ? 1 : 0,
                physicsBodyId: ''  // 将在创建物理体后设置
            });

            // 创建物理体
            let physicsBody = null;
            if (finalPhysicsOptions.isTrigger) {
                // 创建触发器
                const callback = finalPhysicsOptions.triggerCallback || ((event) => {
                    console.log('触发器被触发:', event);
                });
                physicsBody = physicsManager.createTrigger(mesh, callback, {
                    shapeType: finalPhysicsOptions.shapeType,
                    shapeOptions: finalPhysicsOptions.shapeOptions
                });
            } else {
                // 创建普通物理体
                physicsBody = physicsManager.createRigidBody(mesh, finalPhysicsOptions);
            }

            // 设置物理体ID到ECS组件
            if (physicsBody) {
                const bodyId = this.generatePhysicsBodyId();
                Physics.physicsBodyId[entity] = bodyId;
                
                // 在物理管理器中建立映射
                physicsManager.physicsBodies.set(bodyId, {
                    body: physicsBody,
                    mesh: mesh,
                    entity: entity,
                    options: finalPhysicsOptions
                });
            }

            console.log(`创建物理实体成功: ${name} (Entity: ${entity})`);

            return {
                entity: entity,
                mesh: mesh,
                physicsBody: physicsBody,
                name: name
            };

        } catch (error) {
            console.error('创建物理实体失败:', error);
            return null;
        }
    }

    /**
     * 创建物理地面
     * @param {Object} ecsWorld - ECS世界实例
     * @param {PhysicsManager} physicsManager - 物理管理器
     * @param {Scene} scene - Babylon.js场景
     * @param {Object} options - 地面选项
     * @returns {Object} 地面对象
     */
    static createPhysicsGround(ecsWorld, physicsManager, scene, options = {}) {
        const groundOptions = {
            name: 'Ground',
            preset: 'GROUND',
            meshType: 'ground',
            meshOptions: {
                width: options.width || 50,
                height: options.height || 50
            },
            materialColor: options.color || Color3.FromHexString('#8B4513'), // 棕色
            position: new Vector3(0, options.y || 0, 0),
            ...options
        };

        return this.createPhysicsEntity(ecsWorld, physicsManager, scene, groundOptions);
    }

    /**
     * 创建物理球体
     * @param {Object} ecsWorld - ECS世界实例
     * @param {PhysicsManager} physicsManager - 物理管理器
     * @param {Scene} scene - Babylon.js场景
     * @param {Vector3} position - 位置
     * @param {Object} options - 球体选项
     * @returns {Object} 球体对象
     */
    static createPhysicsBall(ecsWorld, physicsManager, scene, position, options = {}) {
        const ballOptions = {
            name: 'Ball',
            preset: 'BALL',
            meshType: 'sphere',
            position: position,
            materialColor: options.color || Color3.Red(),
            ...options
        };

        return this.createPhysicsEntity(ecsWorld, physicsManager, scene, ballOptions);
    }

    /**
     * 创建物理箱子
     * @param {Object} ecsWorld - ECS世界实例
     * @param {PhysicsManager} physicsManager - 物理管理器
     * @param {Scene} scene - Babylon.js场景
     * @param {Vector3} position - 位置
     * @param {Object} options - 箱子选项
     * @returns {Object} 箱子对象
     */
    static createPhysicsBox(ecsWorld, physicsManager, scene, position, options = {}) {
        const boxOptions = {
            name: 'Box',
            preset: 'BOX',
            meshType: 'box',
            position: position,
            materialColor: options.color || Color3.Blue(),
            ...options
        };

        return this.createPhysicsEntity(ecsWorld, physicsManager, scene, boxOptions);
    }

    /**
     * 生成物理体ID
     * @returns {string}
     */
    static generatePhysicsBodyId() {
        return `physics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 计算两点之间的距离
     * @param {Vector3} point1 - 第一个点
     * @param {Vector3} point2 - 第二个点
     * @returns {number} 距离
     */
    static distance(point1, point2) {
        return Vector3.Distance(point1, point2);
    }

    /**
     * 将角度转换为弧度
     * @param {number} degrees - 角度
     * @returns {number} 弧度
     */
    static degreesToRadians(degrees) {
        return degrees * Math.PI / 180;
    }

    /**
     * 将弧度转换为角度
     * @param {number} radians - 弧度
     * @returns {number} 角度
     */
    static radiansToDegrees(radians) {
        return radians * 180 / Math.PI;
    }
}

export default PhysicsUtils;

console.log('PhysicsUtils模块已加载');
