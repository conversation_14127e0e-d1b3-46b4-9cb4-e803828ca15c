// Change this import to check other scenes
import { DefaultSceneWithTexture } from "./scenes/defaultWithTexture";
import { FresnelShaderScene } from "./scenes/fresnelShader";
import { LoadModelAndEnvScene } from "./scenes/loadModelAndEnv";
import { NavigationMeshRecast } from "./scenes/navigationMeshRecast";
import { PhysicsSceneWithAmmo } from "./scenes/physicsWithAmmo";
import { PhysicsSceneWithHavok } from "./scenes/physicsWithHavok";
import { ShanhaijingWorldScene } from "./scenes/shanhaijingWorld";






export const getSceneModule = () => {
    //return new DefaultSceneWithTexture();
    //return new FresnelShaderScene();
    //return new LoadModelAndEnvScene();
    //return new NavigationMeshRecast();
    //return new PhysicsSceneWithAmmo();
    //return new PhysicsSceneWithHavok();
    return new ShanhaijingWorldScene();
};
