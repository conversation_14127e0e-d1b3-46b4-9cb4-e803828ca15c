# 场景管理系统快速参考

## 快速开始

### 1. 基本初始化

```javascript
import SceneManager from './core/SceneManager.js';
import sceneFactory from './core/SceneFactory.js';
import { getDefaultSceneConfig } from './core/SceneConfig.js';

// 创建场景管理器
const sceneManager = new SceneManager(engine);

// 注册场景类型
sceneFactory.getAvailableSceneTypes().forEach(sceneType => {
    const factory = (options) => sceneFactory.createScene(sceneType.type, options);
    sceneManager.registerSceneType(sceneType.type, factory);
});

// 创建并切换到默认场景
const defaultConfig = getDefaultSceneConfig();
await sceneManager.createScene(defaultConfig, canvas);
await sceneManager.switchScene(defaultConfig.id);
```

### 2. 场景切换

```javascript
// 简单切换
await sceneManager.switchScene('scene-id');

// 带过渡效果的切换
await sceneManager.switchScene('scene-id', {
    enableTransition: true,
    fadeTime: 1000
});
```

### 3. 事件监听

```javascript
// 监听场景切换
sceneManager.on('sceneTransitionComplete', (data) => {
    console.log(`切换完成: ${data.from} -> ${data.to}`);
});

// 监听场景创建
sceneManager.on('sceneCreated', (data) => {
    console.log(`场景已创建: ${data.id}`);
});
```

## API 参考

### SceneManager 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `registerSceneType(type, factory)` | type: string, factory: Function | void | 注册场景类型 |
| `createScene(config, canvas)` | config: Object, canvas: HTMLCanvasElement | Promise\<Object\> | 创建场景 |
| `switchScene(sceneId, options)` | sceneId: string, options?: Object | Promise\<Object\> | 切换场景 |
| `disposeScene(sceneId)` | sceneId: string | void | 销毁场景 |
| `getCurrentScene()` | - | Object\|null | 获取当前场景 |
| `getAllScenes()` | - | Array | 获取所有场景信息 |
| `hasScene(sceneId)` | sceneId: string | boolean | 检查场景是否存在 |
| `getPerformanceStats()` | - | Object | 获取性能统计 |

### SceneFactory 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `createScene(type, options)` | type: string, options?: Object | Object | 创建场景实例 |
| `getAvailableSceneTypes()` | - | Array | 获取可用场景类型 |
| `registerSceneType(type, info)` | type: string, info: Object | void | 注册自定义场景类型 |
| `hasSceneType(type)` | type: string | boolean | 检查场景类型是否存在 |

### 场景配置格式

```javascript
const sceneConfig = {
    id: 'unique-scene-id',        // 必需：唯一标识符
    type: 'scene-type',           // 必需：场景类型
    name: '场景名称',              // 可选：显示名称
    description: '场景描述',       // 可选：场景描述
    options: {                    // 可选：场景选项
        // 场景特定配置
    },
    preload: false,               // 可选：是否预加载
    isDefault: false              // 可选：是否为默认场景
};
```

## 内置场景类型

| 类型 | 名称 | 描述 | 分类 |
|------|------|------|------|
| `shanhaijing` | 山海经世界场景 | 完整的MMORPG游戏场景 | game |
| `default` | 默认纹理场景 | 基础演示场景 | basic |
| `fresnel` | 菲涅尔着色器场景 | 着色器效果演示 | shader |
| `modelAndEnv` | 模型环境场景 | 3D模型加载演示 | model |
| `navigation` | 导航网格场景 | AI寻路演示 | navigation |
| `physicsHavok` | Havok物理场景 | Havok物理引擎演示 | physics |
| `physicsAmmo` | Ammo物理场景 | Ammo.js物理引擎演示 | physics |

## 事件列表

### SceneManager 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `sceneTypeRegistered` | {type, factory} | 场景类型注册完成 |
| `sceneCreationStart` | {id, type, options} | 场景创建开始 |
| `sceneCreated` | {id, type, sceneData, creationTime} | 场景创建完成 |
| `sceneCreationError` | {id, type, error} | 场景创建失败 |
| `sceneTransitionStart` | {from, to, options} | 场景切换开始 |
| `sceneTransitionComplete` | {from, to, scene} | 场景切换完成 |
| `sceneTransitionError` | {from, to, error} | 场景切换失败 |
| `sceneDisposed` | {id} | 场景销毁完成 |
| `sceneDisposeError` | {id, error} | 场景销毁失败 |
| `scenePreloaded` | {id, sceneData} | 场景预加载完成 |

## 常用代码片段

### 创建自定义场景

```javascript
class MyCustomScene {
    constructor() {
        this.scene = null;
    }
    
    async createScene(engine, canvas) {
        this.scene = new Scene(engine);
        // 场景初始化逻辑
        return this.scene;
    }
    
    dispose() {
        if (this.scene) {
            this.scene.dispose();
        }
    }
}

// 注册自定义场景
sceneFactory.registerSceneType('myCustom', {
    name: '我的自定义场景',
    description: '自定义场景描述',
    factory: (options) => new MyCustomScene(),
    category: 'custom'
});
```

### 场景预加载

```javascript
// 预加载场景
const preloadConfig = {
    id: 'preload-scene',
    type: 'shanhaijing',
    options: {}
};

await sceneManager.preloadScene(preloadConfig, canvas);

// 快速切换到预加载的场景
await sceneManager.switchScene('preload-scene');
```

### 性能监控

```javascript
// 定期检查性能
setInterval(() => {
    const stats = sceneManager.getPerformanceStats();
    console.log('场景管理器状态:', {
        总场景数: stats.totalScenes,
        当前场景: stats.currentSceneId,
        内存使用: stats.memoryUsage?.used + 'MB'
    });
}, 5000);
```

### 错误处理

```javascript
try {
    await sceneManager.createScene(config, canvas);
    await sceneManager.switchScene(config.id);
} catch (error) {
    console.error('场景操作失败:', error);
    
    // 回退到安全场景
    const fallbackConfig = getDefaultSceneConfig();
    await sceneManager.switchScene(fallbackConfig.id);
}
```

## 调试技巧

### 1. 启用详细日志

```javascript
// 监听所有事件
['sceneCreated', 'sceneTransitionComplete', 'sceneDisposed'].forEach(event => {
    sceneManager.on(event, (data) => {
        console.log(`[SceneManager] ${event}:`, data);
    });
});
```

### 2. 全局访问（开发环境）

```javascript
if (process.env.NODE_ENV === 'development') {
    window.sceneManager = sceneManager;
    window.sceneFactory = sceneFactory;
    
    // 控制台快捷命令
    window.switchToScene = (sceneId) => sceneManager.switchScene(sceneId);
    window.listScenes = () => sceneManager.getAllScenes();
}
```

### 3. 性能分析

```javascript
// 场景切换性能测试
async function benchmarkSceneSwitch(sceneId) {
    const startTime = performance.now();
    await sceneManager.switchScene(sceneId);
    const endTime = performance.now();
    
    console.log(`场景切换耗时: ${endTime - startTime}ms`);
}
```

## 最佳实践

### ✅ 推荐做法

- 始终使用 `await` 处理异步操作
- 及时清理不需要的场景
- 使用预加载提升用户体验
- 监听事件进行状态管理
- 实现错误恢复机制

### ❌ 避免做法

- 不要忘记处理异步操作的错误
- 不要在场景切换过程中进行其他场景操作
- 不要手动操作 Babylon.js 场景，使用场景管理器
- 不要在生产环境暴露调试接口
- 不要忽略内存清理

## 故障排除

### 常见错误

1. **"场景不存在"** - 检查场景ID是否正确，是否已创建
2. **"未注册的场景类型"** - 确保场景类型已注册到工厂
3. **"场景创建失败"** - 检查场景配置和依赖资源
4. **内存泄漏** - 确保正确调用 `disposeScene()`
5. **切换卡顿** - 使用预加载或优化场景资源

### 调试命令

```javascript
// 检查场景状态
console.log('当前场景:', sceneManager.getCurrentScene());
console.log('所有场景:', sceneManager.getAllScenes());
console.log('性能统计:', sceneManager.getPerformanceStats());

// 检查场景类型
console.log('可用类型:', sceneFactory.getAvailableSceneTypes());
console.log('已注册类型:', sceneManager.getRegisteredSceneTypes());
```

## 更多资源

- [完整实现文档](./场景管理系统实现.md)
- [技术规范](./technical-specifications.md)
- [单元测试示例](../src/core/SceneManager.unit.test.js)
- [演示页面](../public/scene-manager-demo.html)
