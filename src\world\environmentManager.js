// src/world/environmentManager.js
// 环境管理器 - 负责管理昼夜循环、天气系统、光照等环境效果

import {
    Scene,
    Vector3,
    Color3,
    HemisphericLight,
    DirectionalLight,
    StandardMaterial,
    CubeTexture,
    Texture,
    Animation,
    MeshBuilder
} from "@babylonjs/core";

import { EnvironmentConfig, EnvironmentTransition } from "../config/environment.js";

/**
 * 环境管理器类
 * 负责管理游戏世界的环境效果，包括昼夜循环、天气系统、光照等
 */
export class EnvironmentManager {
    /**
     * 构造函数
     * @param {Scene} scene - Babylon.js场景实例
     * @param {Object} options - 环境配置选项
     */
    constructor(scene, options = {}) {
        this.scene = scene;
        this.config = { ...EnvironmentConfig, ...options };

        // 时间系统
        this.timeOfDay = this.config.dayNightCycle.startTime; // 0-1，表示一天中的时间
        this.currentSeason = 'spring';
        this.currentWeather = 'clear';
        this.isTransitioning = false;

        // 光照系统
        this.sunLight = null;
        this.ambientLight = null;
        this.moonLight = null;

        // 天空系统
        this.skybox = null;
        this.skyMaterial = null;

        // 动画系统
        this.dayNightAnimation = null;
        this.weatherAnimation = null;

        // 性能优化
        this.lastUpdateTime = 0;
        this.updateInterval = this.config.updateInterval || 1000;

        console.log("环境管理器已初始化");
        this.initialize();
    }

    /**
     * 初始化环境系统
     */
    async initialize() {
        console.log("初始化环境系统...");

        // 初始化光照系统
        this.initializeLighting();

        // 初始化天空系统
        this.initializeSkybox();

        // 初始化雾效
        this.initializeFog();

        // 启动昼夜循环
        this.startDayNightCycle();

        console.log("环境系统初始化完成");
    }

    /**
     * 初始化光照系统
     */
    initializeLighting() {
        console.log("初始化光照系统...");

        // 创建太阳光（主要方向光）
        this.sunLight = new DirectionalLight(
            "sunLight",
            new Vector3(-1, -1, -1),
            this.scene
        );
        this.sunLight.intensity = this.config.dayNightCycle.sunIntensity.day;
        this.sunLight.diffuse = new Color3(1.0, 1.0, 0.9);
        this.sunLight.specular = new Color3(1.0, 1.0, 0.9);

        // 创建环境光（半球光）
        this.ambientLight = new HemisphericLight(
            "ambientLight",
            new Vector3(0, 1, 0),
            this.scene
        );
        this.ambientLight.intensity = 0.3;
        this.ambientLight.diffuse = new Color3(...this.config.dayNightCycle.ambientColor.day);

        // 创建月光（夜晚的方向光）
        this.moonLight = new DirectionalLight(
            "moonLight",
            new Vector3(1, -0.5, 1),
            this.scene
        );
        this.moonLight.intensity = 0.1;
        this.moonLight.diffuse = new Color3(0.7, 0.8, 1.0);
        this.moonLight.enabled = false;

        console.log("光照系统初始化完成");
    }

    /**
     * 初始化天空盒
     */
    initializeSkybox() {
        console.log("初始化天空盒...");

        // 创建天空盒网格
        this.skybox = MeshBuilder.CreateSphere("skybox", { diameter: 1000 }, this.scene);

        // 创建天空盒材质
        this.skyMaterial = new StandardMaterial("skyMaterial", this.scene);
        this.skyMaterial.backFaceCulling = false;
        this.skyMaterial.disableLighting = true;

        // 创建程序化天空纹理（简化版）
        this.createProceduralSky();

        this.skybox.material = this.skyMaterial;
        this.skybox.infiniteDistance = true;

        console.log("天空盒初始化完成");
    }

    /**
     * 创建程序化天空
     */
    createProceduralSky() {
        // 这里创建一个简单的渐变天空
        // 在实际项目中，可以使用更复杂的程序化天空或HDR贴图

        const skyTexture = new Texture("data:image/svg+xml;base64," + btoa(`
            <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#E0F6FF;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="512" height="512" fill="url(#skyGradient)" />
            </svg>
        `), this.scene);

        this.skyMaterial.reflectionTexture = skyTexture;
        this.skyMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
    }

    /**
     * 初始化雾效
     */
    initializeFog() {
        console.log("初始化雾效...");

        // 使用Scene的内置雾效模式
        this.scene.fogMode = Scene.FOGMODE_EXP2;
        this.scene.fogDensity = this.config.dayNightCycle.fog.day.density;
        this.scene.fogColor = new Color3(...this.config.dayNightCycle.fog.day.color);

        console.log("雾效初始化完成");
    }

    /**
     * 启动昼夜循环
     */
    startDayNightCycle() {
        console.log("启动昼夜循环...");

        // 使用简单的时间更新机制而不是复杂的动画系统
        this.dayNightStartTime = performance.now();
        this.isDayNightCycleActive = true;

        console.log("昼夜循环已启动");
    }

    /**
     * 更新环境系统
     * @param {number} deltaTime - 时间增量（秒）
     */
    update(deltaTime) {
        const currentTime = performance.now();

        // 性能优化：限制更新频率
        if (currentTime - this.lastUpdateTime < this.updateInterval) {
            return;
        }

        this.lastUpdateTime = currentTime;

        // 更新昼夜循环时间
        if (this.isDayNightCycleActive) {
            const elapsed = currentTime - this.dayNightStartTime;
            const cycleProgress = (elapsed % this.config.dayNightCycle.duration) / this.config.dayNightCycle.duration;
            this.timeOfDay = (this.config.dayNightCycle.startTime + cycleProgress) % 1.0;
        }

        // 更新昼夜循环
        this.updateDayNightCycle();

        // 更新天气效果
        this.updateWeatherEffects();

        // 更新季节效果
        this.updateSeasonalEffects();
    }

    /**
     * 更新昼夜循环
     */
    updateDayNightCycle() {
        const timeOfDay = this.timeOfDay;

        // 计算当前时间段
        let currentPeriod, nextPeriod, blendFactor;

        if (timeOfDay < 0.25) { // 夜晚到黎明
            currentPeriod = 'night';
            nextPeriod = 'dawn';
            blendFactor = timeOfDay / 0.25;
        } else if (timeOfDay < 0.5) { // 黎明到白天
            currentPeriod = 'dawn';
            nextPeriod = 'day';
            blendFactor = (timeOfDay - 0.25) / 0.25;
        } else if (timeOfDay < 0.75) { // 白天到黄昏
            currentPeriod = 'day';
            nextPeriod = 'dusk';
            blendFactor = (timeOfDay - 0.5) / 0.25;
        } else { // 黄昏到夜晚
            currentPeriod = 'dusk';
            nextPeriod = 'night';
            blendFactor = (timeOfDay - 0.75) / 0.25;
        }

        // 更新太阳光照
        this.updateSunLighting(currentPeriod, nextPeriod, blendFactor);

        // 更新环境光
        this.updateAmbientLighting(currentPeriod, nextPeriod, blendFactor);

        // 更新雾效
        this.updateFogEffects(currentPeriod, nextPeriod, blendFactor);

        // 更新天空颜色
        this.updateSkyColor(currentPeriod, nextPeriod, blendFactor);
    }

    /**
     * 更新太阳光照
     */
    updateSunLighting(currentPeriod, nextPeriod, blendFactor) {
        const config = this.config.dayNightCycle;

        // 插值计算光照强度
        const currentIntensity = config.sunIntensity[currentPeriod];
        const nextIntensity = config.sunIntensity[nextPeriod];
        const intensity = this.lerp(currentIntensity, nextIntensity, blendFactor);

        // 插值计算光照颜色
        const currentColor = config.sunColor[currentPeriod];
        const nextColor = config.sunColor[nextPeriod];
        const color = this.lerpColor(currentColor, nextColor, blendFactor);

        // 应用到太阳光
        this.sunLight.intensity = intensity;
        this.sunLight.diffuse = new Color3(...color);
        this.sunLight.specular = new Color3(...color);

        // 更新太阳位置
        this.updateSunPosition();

        // 控制月光
        this.moonLight.enabled = intensity < 0.2;
        if (this.moonLight.enabled) {
            this.moonLight.intensity = Math.max(0.05, 0.2 - intensity);
        }
    }

    /**
     * 更新太阳位置
     */
    updateSunPosition() {
        // 根据时间计算太阳位置
        const angle = (this.timeOfDay - 0.25) * Math.PI * 2; // 从东方开始
        const elevation = Math.sin(angle) * 0.8; // 高度角
        const azimuth = Math.cos(angle);

        const sunDirection = new Vector3(
            azimuth,
            -Math.abs(elevation), // 太阳总是在上方
            Math.sin(angle * 0.5)
        ).normalize();

        this.sunLight.direction = sunDirection;
    }

    /**
     * 更新环境光
     */
    updateAmbientLighting(currentPeriod, nextPeriod, blendFactor) {
        const config = this.config.dayNightCycle;

        const currentColor = config.ambientColor[currentPeriod];
        const nextColor = config.ambientColor[nextPeriod];
        const color = this.lerpColor(currentColor, nextColor, blendFactor);

        this.ambientLight.diffuse = new Color3(...color);
    }

    /**
     * 更新雾效
     */
    updateFogEffects(currentPeriod, nextPeriod, blendFactor) {
        const config = this.config.dayNightCycle;

        const currentFog = config.fog[currentPeriod];
        const nextFog = config.fog[nextPeriod];

        const density = this.lerp(currentFog.density, nextFog.density, blendFactor);
        const color = this.lerpColor(currentFog.color, nextFog.color, blendFactor);

        this.scene.fogDensity = density;
        this.scene.fogColor = new Color3(...color);
    }

    /**
     * 更新天空颜色
     */
    updateSkyColor(currentPeriod, nextPeriod, blendFactor) {
        // 这里可以实现更复杂的天空颜色变化
        // 目前保持简单的实现
    }

    /**
     * 更新天气效果
     */
    updateWeatherEffects() {
        const weatherConfig = this.config.weather[this.currentWeather];

        // 应用天气对光照的影响
        if (this.sunLight) {
            this.sunLight.intensity *= weatherConfig.lightModifier;
        }

        // 应用天气对雾效的影响
        this.scene.fogDensity *= (1 + weatherConfig.fogDensity);
    }

    /**
     * 更新季节效果
     */
    updateSeasonalEffects() {
        const seasonConfig = this.config.seasons[this.currentSeason];

        // 这里可以实现季节性的环境变化
        // 例如植被颜色、光照强度等
    }

    /**
     * 设置天气
     * @param {string} weatherType - 天气类型
     * @param {boolean} immediate - 是否立即切换
     */
    setWeather(weatherType, immediate = false) {
        if (!this.config.weather[weatherType]) {
            console.warn(`未知的天气类型: ${weatherType}`);
            return;
        }

        console.log(`切换天气到: ${weatherType}`);

        if (immediate) {
            this.currentWeather = weatherType;
        } else {
            // 实现平滑的天气过渡
            this.transitionToWeather(weatherType);
        }
    }

    /**
     * 天气过渡
     * @param {string} targetWeather - 目标天气
     */
    transitionToWeather(targetWeather) {
        if (this.isTransitioning) {
            return;
        }

        this.isTransitioning = true;
        const transitionDuration = EnvironmentTransition.weatherTransition;

        // 创建天气过渡动画
        setTimeout(() => {
            this.currentWeather = targetWeather;
            this.isTransitioning = false;
            console.log(`天气过渡完成: ${targetWeather}`);
        }, transitionDuration);
    }

    /**
     * 设置季节
     * @param {string} season - 季节名称
     */
    setSeason(season) {
        if (!this.config.seasons[season]) {
            console.warn(`未知的季节: ${season}`);
            return;
        }

        console.log(`切换季节到: ${season}`);
        this.currentSeason = season;
    }

    /**
     * 设置时间
     * @param {number} time - 时间（0-1）
     */
    setTimeOfDay(time) {
        this.timeOfDay = Math.max(0, Math.min(1, time));
    }

    /**
     * 获取当前时间信息
     * @returns {Object} 时间信息
     */
    getTimeInfo() {
        const hours = Math.floor(this.timeOfDay * 24);
        const minutes = Math.floor((this.timeOfDay * 24 - hours) * 60);

        return {
            timeOfDay: this.timeOfDay,
            hours: hours,
            minutes: minutes,
            period: this.getCurrentPeriod(),
            weather: this.currentWeather,
            season: this.currentSeason
        };
    }

    /**
     * 获取当前时间段
     * @returns {string} 时间段名称
     */
    getCurrentPeriod() {
        if (this.timeOfDay < 0.25) return 'night';
        if (this.timeOfDay < 0.5) return 'dawn';
        if (this.timeOfDay < 0.75) return 'day';
        return 'dusk';
    }

    /**
     * 线性插值
     * @param {number} a - 起始值
     * @param {number} b - 结束值
     * @param {number} t - 插值因子（0-1）
     * @returns {number} 插值结果
     */
    lerp(a, b, t) {
        return a + (b - a) * t;
    }

    /**
     * 颜色插值
     * @param {Array} colorA - 起始颜色 [r, g, b]
     * @param {Array} colorB - 结束颜色 [r, g, b]
     * @param {number} t - 插值因子（0-1）
     * @returns {Array} 插值后的颜色
     */
    lerpColor(colorA, colorB, t) {
        return [
            this.lerp(colorA[0], colorB[0], t),
            this.lerp(colorA[1], colorB[1], t),
            this.lerp(colorA[2], colorB[2], t)
        ];
    }

    /**
     * 释放资源
     */
    dispose() {
        // 停止昼夜循环
        this.isDayNightCycleActive = false;

        // 释放光照
        if (this.sunLight) this.sunLight.dispose();
        if (this.ambientLight) this.ambientLight.dispose();
        if (this.moonLight) this.moonLight.dispose();

        // 释放天空盒
        if (this.skybox) this.skybox.dispose();
        if (this.skyMaterial) this.skyMaterial.dispose();

        console.log("环境管理器资源已释放");
    }
}

export default EnvironmentManager;
