# 场景管理系统实现文档

## 概述

场景管理系统（SceneManager）是山海经MMORPG项目的核心架构组件，负责管理Babylon.js场景的生命周期、处理场景切换和资源清理，以及优化渲染性能。该系统采用工厂模式和事件驱动架构，提供了灵活、可扩展的场景管理解决方案。

## 系统架构

### 核心组件

1. **SceneManager** (`src/core/SceneManager.js`) - 场景管理器核心类
2. **SceneFactory** (`src/core/SceneFactory.js`) - 场景工厂类
3. **SceneConfig** (`src/core/SceneConfig.js`) - 场景配置定义
4. **EventEmitter** (`src/utils/eventEmitter.js`) - 事件系统基础

### 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SceneManager  │────│  SceneFactory   │────│   SceneConfig   │
│   (管理器核心)   │    │   (工厂模式)     │    │   (配置定义)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   EventEmitter  │    │  Scene Classes  │    │  Default Configs│
│   (事件驱动)     │    │  (具体场景类)    │    │  (预设配置)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 实现思路

### 1. 设计模式应用

#### 工厂模式 (Factory Pattern)
- **目的**: 解耦场景创建逻辑，支持动态场景类型注册
- **实现**: SceneFactory类负责根据场景类型创建对应的场景实例
- **优势**: 易于扩展新场景类型，无需修改核心管理代码

#### 观察者模式 (Observer Pattern)
- **目的**: 实现事件驱动的场景状态通知
- **实现**: 基于EventEmitter的事件系统
- **优势**: 松耦合的组件通信，便于调试和监控

#### 单例模式 (Singleton Pattern)
- **目的**: 确保场景工厂的全局唯一性
- **实现**: SceneFactory导出单例实例
- **优势**: 统一的场景类型管理

### 2. 生命周期管理

```
场景生命周期流程:
创建 → 激活 → 运行 → 暂停 → 恢复 → 销毁

┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│  Create │───▶│ Activate│───▶│  Running│───▶│ Dispose │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
                      │              │              ▲
                      │              ▼              │
                      │         ┌─────────┐         │
                      │         │  Pause  │         │
                      │         └─────────┘         │
                      │              │              │
                      │              ▼              │
                      │         ┌─────────┐         │
                      └────────▶│ Resume  │─────────┘
                                └─────────┘
```

### 3. 内存管理策略

- **场景缓存**: 已创建的场景保存在内存中，避免重复创建开销
- **资源清理**: 提供显式的dispose方法，确保资源正确释放
- **预加载机制**: 支持场景预加载，提升用户体验
- **内存监控**: 集成性能统计，监控内存使用情况

## 代码导读

### SceneManager 核心类

#### 主要属性
```javascript
class SceneManager extends EventEmitter {
    constructor(engine) {
        this.engine = engine;              // Babylon.js引擎实例
        this.currentScene = null;          // 当前活动场景
        this.currentSceneId = null;        // 当前场景ID
        this.scenes = new Map();           // 场景存储映射
        this.sceneFactories = new Map();   // 场景工厂映射
        this.preloadedScenes = new Map();  // 预加载场景映射
        this.isTransitioning = false;      // 场景切换状态
        this.performanceStats = {};        // 性能统计
    }
}
```

#### 核心方法解析

**1. registerSceneType(type, factory)**
```javascript
// 注册场景类型工厂
registerSceneType(type, factory) {
    if (typeof factory !== 'function') {
        throw new Error(`场景工厂必须是函数: ${type}`);
    }
    this.sceneFactories.set(type, factory);
    this.emit('sceneTypeRegistered', { type, factory });
}
```
- 验证工厂函数有效性
- 存储类型与工厂的映射关系
- 触发注册完成事件

**2. createScene(sceneConfig, canvas)**
```javascript
async createScene(sceneConfig, canvas) {
    const { id, type, options = {} } = sceneConfig;

    // 防重复创建
    if (this.scenes.has(id)) {
        return this.scenes.get(id);
    }

    // 获取并调用工厂函数
    const factory = this.sceneFactories.get(type);
    const sceneInstance = factory(options);
    const babylonScene = await sceneInstance.createScene(this.engine, canvas);

    // 存储场景数据
    const sceneData = { id, type, options, instance: sceneInstance, babylonScene };
    this.scenes.set(id, sceneData);

    return sceneData;
}
```
- 配置验证和重复检查
- 工厂模式创建场景实例
- 性能监控和事件通知

**3. switchScene(sceneId, transitionOptions)**
```javascript
async switchScene(sceneId, transitionOptions = {}) {
    // 防并发切换
    if (this.isTransitioning) {
        await this.transitionPromise;
    }

    // 执行场景切换
    this.isTransitioning = true;
    this.transitionPromise = this._performSceneSwitch(targetScene, transitionOptions);

    return await this.transitionPromise;
}
```
- 并发控制，防止同时切换多个场景
- 支持过渡效果配置
- 异步操作，确保切换完成

### SceneFactory 工厂类

#### 场景类型注册
```javascript
registerBuiltinScenes() {
    // 注册山海经世界场景
    this.sceneTypes.set('shanhaijing', {
        name: '山海经世界场景',
        description: '完整的山海经主题MMORPG场景',
        factory: (options) => new ShanhaijingWorldScene(),
        category: 'game'
    });

    // 注册其他场景类型...
}
```

#### 场景实例创建
```javascript
createScene(type, options = {}) {
    const sceneInfo = this.sceneTypes.get(type);
    if (!sceneInfo) {
        throw new Error(`未知的场景类型: ${type}`);
    }

    const sceneInstance = sceneInfo.factory(options);

    // 添加元数据
    sceneInstance._sceneType = type;
    sceneInstance._sceneName = sceneInfo.name;
    sceneInstance._createdAt = Date.now();

    return sceneInstance;
}
```

### SceneConfig 配置系统

#### 默认配置结构
```javascript
export const DefaultSceneConfigs = {
    shanhaijingWorld: {
        id: 'shanhaijing-main',
        type: 'shanhaijing',
        name: '山海经世界',
        options: {
            terrain: { type: 'mountains', seed: 55555 },
            environment: { enableDayNightCycle: true },
            player: { spawnPosition: [0, 10, 0] },
            camera: { type: 'arcRotate', distance: 30 }
        },
        preload: true,
        isDefault: true
    }
};
```

## 使用方法

### 1. 基本初始化

```javascript
import SceneManager from './core/SceneManager.js';
import sceneFactory from './core/SceneFactory.js';
import { getDefaultSceneConfig } from './core/SceneConfig.js';

// 创建场景管理器
const sceneManager = new SceneManager(engine);

// 注册场景类型
const availableSceneTypes = sceneFactory.getAvailableSceneTypes();
for (const sceneTypeInfo of availableSceneTypes) {
    const factory = (options) => sceneFactory.createScene(sceneTypeInfo.type, options);
    sceneManager.registerSceneType(sceneTypeInfo.type, factory);
}

// 创建默认场景
const defaultConfig = getDefaultSceneConfig();
const sceneData = await sceneManager.createScene(defaultConfig, canvas);
await sceneManager.switchScene(defaultConfig.id);
```

### 2. 场景切换

```javascript
// 创建新场景配置
const newSceneConfig = {
    id: 'physics-demo',
    type: 'physicsHavok',
    options: {
        gravity: [0, -9.81, 0],
        enableDebugDraw: true
    }
};

// 创建并切换场景
await sceneManager.createScene(newSceneConfig, canvas);
await sceneManager.switchScene('physics-demo', {
    enableTransition: true,
    fadeTime: 1000
});
```

### 3. 事件监听

```javascript
// 监听场景切换事件
sceneManager.on('sceneTransitionComplete', (data) => {
    console.log(`场景切换完成: ${data.from} -> ${data.to}`);
});

// 监听场景创建事件
sceneManager.on('sceneCreated', (data) => {
    console.log(`新场景已创建: ${data.id} (${data.type})`);
});

// 监听场景销毁事件
sceneManager.on('sceneDisposed', (data) => {
    console.log(`场景已销毁: ${data.id}`);
});
```

### 4. 性能监控

```javascript
// 获取性能统计
const stats = sceneManager.getPerformanceStats();
console.log('场景管理器性能统计:', {
    totalScenes: stats.totalScenes,
    currentScene: stats.currentSceneId,
    memoryUsage: stats.memoryUsage,
    lastSwitchTime: stats.lastSwitchTime
});

// 获取所有场景信息
const allScenes = sceneManager.getAllScenes();
allScenes.forEach(scene => {
    console.log(`场景: ${scene.id}, 类型: ${scene.type}, 活动: ${scene.isActive}`);
});
```

### 5. 自定义场景类型

```javascript
// 定义自定义场景类
class CustomScene {
    constructor() {
        this.scene = null;
    }

    async createScene(engine, canvas) {
        this.scene = new Scene(engine);
        // 自定义场景初始化逻辑
        return this.scene;
    }

    dispose() {
        if (this.scene) {
            this.scene.dispose();
        }
    }
}

// 注册自定义场景类型
sceneFactory.registerSceneType('custom', {
    name: '自定义场景',
    description: '用户自定义的场景类型',
    factory: (options) => new CustomScene(),
    category: 'custom'
});
```

## 注意事项

### 1. 内存管理

**重要**: 必须正确管理场景生命周期，避免内存泄漏

```javascript
// ❌ 错误做法 - 不清理旧场景
await sceneManager.switchScene('new-scene');

// ✅ 正确做法 - 清理不需要的场景
await sceneManager.switchScene('new-scene');
sceneManager.disposeScene('old-scene'); // 清理旧场景
```

### 2. 异步操作处理

**重要**: 场景创建和切换都是异步操作，必须正确处理

```javascript
// ❌ 错误做法 - 不等待异步完成
sceneManager.createScene(config, canvas);
sceneManager.switchScene(config.id); // 可能失败

// ✅ 正确做法 - 正确处理异步
await sceneManager.createScene(config, canvas);
await sceneManager.switchScene(config.id);
```

### 3. 错误处理

```javascript
try {
    await sceneManager.createScene(config, canvas);
    await sceneManager.switchScene(config.id);
} catch (error) {
    console.error('场景操作失败:', error);
    // 实现错误恢复逻辑
    await sceneManager.switchScene('fallback-scene');
}
```

### 4. 性能优化建议

- **预加载**: 对常用场景使用预加载机制
- **资源复用**: 相同类型场景可以复用资源
- **内存监控**: 定期检查内存使用情况
- **批量操作**: 避免频繁的场景切换

### 5. 调试技巧

```javascript
// 启用详细日志
sceneManager.on('sceneCreationStart', console.log);
sceneManager.on('sceneTransitionStart', console.log);

// 全局访问（仅开发环境）
if (process.env.NODE_ENV === 'development') {
    window.sceneManager = sceneManager;
    window.sceneFactory = sceneFactory;
}
```

## 扩展指南

### 添加新场景类型

1. **创建场景类**: 实现 `createScene()` 和 `dispose()` 方法
2. **注册场景类型**: 在SceneFactory中注册新类型
3. **添加配置**: 在SceneConfig中添加默认配置
4. **测试验证**: 编写单元测试确保功能正常

### 自定义过渡效果

```javascript
// 扩展场景管理器，添加自定义过渡效果
class ExtendedSceneManager extends SceneManager {
    async _fadeOut(duration) {
        // 实现自定义淡出效果
        return new Promise(resolve => {
            // 自定义动画逻辑
            setTimeout(resolve, duration);
        });
    }
}
```

## 实践案例

### 案例1: 游戏关卡切换

```javascript
// 游戏关卡管理示例
class GameLevelManager {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.currentLevel = 0;
        this.maxLevel = 10;
    }

    async loadLevel(levelNumber) {
        const levelConfig = {
            id: `level-${levelNumber}`,
            type: 'shanhaijing',
            options: {
                terrain: { seed: levelNumber * 1000 },
                difficulty: levelNumber,
                enemies: this.getEnemiesForLevel(levelNumber)
            }
        };

        // 预加载下一关
        if (levelNumber < this.maxLevel) {
            this.preloadNextLevel(levelNumber + 1);
        }

        await this.sceneManager.createScene(levelConfig, canvas);
        await this.sceneManager.switchScene(levelConfig.id, {
            enableTransition: true,
            fadeTime: 2000
        });

        this.currentLevel = levelNumber;
    }

    async preloadNextLevel(levelNumber) {
        const nextLevelConfig = {
            id: `level-${levelNumber}`,
            type: 'shanhaijing',
            options: { terrain: { seed: levelNumber * 1000 } }
        };

        await this.sceneManager.preloadScene(nextLevelConfig, canvas);
    }
}
```

### 案例2: 场景状态持久化

```javascript
// 场景状态管理
class SceneStateManager {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.sceneStates = new Map();

        // 监听场景切换，保存状态
        sceneManager.on('sceneTransitionStart', (data) => {
            this.saveSceneState(data.from);
        });

        sceneManager.on('sceneTransitionComplete', (data) => {
            this.restoreSceneState(data.to);
        });
    }

    saveSceneState(sceneId) {
        const scene = this.sceneManager.getCurrentScene();
        if (scene && scene.instance.getState) {
            const state = scene.instance.getState();
            this.sceneStates.set(sceneId, state);

            // 持久化到本地存储
            localStorage.setItem(`scene_state_${sceneId}`, JSON.stringify(state));
        }
    }

    restoreSceneState(sceneId) {
        let state = this.sceneStates.get(sceneId);

        // 从本地存储恢复
        if (!state) {
            const saved = localStorage.getItem(`scene_state_${sceneId}`);
            if (saved) {
                state = JSON.parse(saved);
                this.sceneStates.set(sceneId, state);
            }
        }

        const scene = this.sceneManager.getCurrentScene();
        if (scene && scene.instance.setState && state) {
            scene.instance.setState(state);
        }
    }
}
```

### 案例3: 动态场景加载

```javascript
// 动态场景加载器
class DynamicSceneLoader {
    constructor(sceneManager, sceneFactory) {
        this.sceneManager = sceneManager;
        this.sceneFactory = sceneFactory;
        this.loadingQueue = [];
        this.isLoading = false;
    }

    async loadSceneFromConfig(configUrl) {
        try {
            // 从远程加载场景配置
            const response = await fetch(configUrl);
            const sceneConfig = await response.json();

            // 验证配置
            if (!this.validateSceneConfig(sceneConfig)) {
                throw new Error('无效的场景配置');
            }

            // 动态注册场景类型（如果需要）
            if (sceneConfig.customSceneClass) {
                await this.loadCustomSceneClass(sceneConfig.customSceneClass);
            }

            // 创建并切换场景
            await this.sceneManager.createScene(sceneConfig, canvas);
            await this.sceneManager.switchScene(sceneConfig.id);

            return sceneConfig;

        } catch (error) {
            console.error('动态场景加载失败:', error);
            throw error;
        }
    }

    async loadCustomSceneClass(classConfig) {
        // 动态导入自定义场景类
        const module = await import(classConfig.modulePath);
        const SceneClass = module[classConfig.className];

        // 注册到工厂
        this.sceneFactory.registerSceneType(classConfig.type, {
            name: classConfig.name,
            description: classConfig.description,
            factory: (options) => new SceneClass(options),
            category: 'dynamic'
        });
    }

    validateSceneConfig(config) {
        return config.id && config.type && config.name;
    }
}
```

## 性能优化深度解析

### 1. 内存池管理

```javascript
// 场景对象池
class SceneObjectPool {
    constructor() {
        this.pools = new Map();
    }

    getPool(sceneType) {
        if (!this.pools.has(sceneType)) {
            this.pools.set(sceneType, {
                available: [],
                inUse: new Set()
            });
        }
        return this.pools.get(sceneType);
    }

    acquireScene(sceneType, factory) {
        const pool = this.getPool(sceneType);

        let scene;
        if (pool.available.length > 0) {
            scene = pool.available.pop();
            scene.reset(); // 重置场景状态
        } else {
            scene = factory();
        }

        pool.inUse.add(scene);
        return scene;
    }

    releaseScene(sceneType, scene) {
        const pool = this.getPool(sceneType);

        if (pool.inUse.has(scene)) {
            pool.inUse.delete(scene);
            pool.available.push(scene);
        }
    }
}
```

### 2. 渐进式加载

```javascript
// 渐进式场景加载
class ProgressiveSceneLoader {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.loadingStages = [
            'basic',      // 基础几何体
            'textures',   // 纹理资源
            'lighting',   // 光照系统
            'physics',    // 物理系统
            'audio',      // 音频系统
            'effects'     // 特效系统
        ];
    }

    async loadSceneProgressively(sceneConfig, onProgress) {
        const totalStages = this.loadingStages.length;

        for (let i = 0; i < totalStages; i++) {
            const stage = this.loadingStages[i];

            await this.loadStage(sceneConfig, stage);

            const progress = (i + 1) / totalStages;
            onProgress && onProgress(progress, stage);
        }
    }

    async loadStage(sceneConfig, stage) {
        // 根据阶段加载不同的资源
        switch (stage) {
            case 'basic':
                await this.loadBasicGeometry(sceneConfig);
                break;
            case 'textures':
                await this.loadTextures(sceneConfig);
                break;
            // ... 其他阶段
        }
    }
}
```

## 故障排除指南

### 常见问题及解决方案

#### 1. 场景切换卡顿

**问题**: 场景切换时出现明显的卡顿或延迟

**原因分析**:
- 场景资源过大，加载时间长
- 没有使用预加载机制
- 同步操作阻塞主线程

**解决方案**:
```javascript
// 使用预加载和异步操作
async function optimizedSceneSwitch(targetSceneId) {
    // 1. 预加载目标场景
    await sceneManager.preloadScene(targetSceneConfig, canvas);

    // 2. 使用渐进式切换
    await sceneManager.switchScene(targetSceneId, {
        enableTransition: true,
        fadeTime: 500,
        preloadAssets: true
    });
}
```

#### 2. 内存泄漏

**问题**: 长时间运行后内存使用持续增长

**原因分析**:
- 场景销毁不完整
- 事件监听器未清理
- 纹理和几何体未释放

**解决方案**:
```javascript
// 完整的场景清理
class ProperSceneCleanup {
    disposeScene(sceneId) {
        const sceneData = this.scenes.get(sceneId);
        if (sceneData) {
            // 1. 清理事件监听器
            sceneData.instance.removeAllListeners();

            // 2. 释放Babylon.js资源
            sceneData.babylonScene.dispose();

            // 3. 清理自定义资源
            if (sceneData.instance.dispose) {
                sceneData.instance.dispose();
            }

            // 4. 从映射中移除
            this.scenes.delete(sceneId);

            // 5. 强制垃圾回收（开发环境）
            if (window.gc) {
                window.gc();
            }
        }
    }
}
```

#### 3. 场景状态不一致

**问题**: 场景切换后状态异常或数据丢失

**解决方案**:
```javascript
// 状态一致性保证
class StateConsistencyManager {
    async switchSceneWithStateCheck(targetSceneId) {
        // 1. 保存当前状态
        const currentState = this.captureCurrentState();

        try {
            // 2. 执行切换
            await sceneManager.switchScene(targetSceneId);

            // 3. 验证新场景状态
            const newState = this.captureCurrentState();
            this.validateStateTransition(currentState, newState);

        } catch (error) {
            // 4. 错误恢复
            console.error('场景切换失败，尝试恢复:', error);
            await this.recoverFromFailedSwitch(currentState);
            throw error;
        }
    }
}
```

## 测试策略

### 单元测试示例

```javascript
// 场景管理器测试套件
describe('SceneManager Integration Tests', () => {
    let sceneManager;
    let mockEngine;
    let mockCanvas;

    beforeEach(() => {
        mockEngine = createMockEngine();
        mockCanvas = createMockCanvas();
        sceneManager = new SceneManager(mockEngine);
    });

    test('应该正确处理场景切换序列', async () => {
        // 创建多个场景
        const scenes = ['scene1', 'scene2', 'scene3'];

        for (const sceneId of scenes) {
            await sceneManager.createScene({
                id: sceneId,
                type: 'test'
            }, mockCanvas);
        }

        // 测试切换序列
        for (const sceneId of scenes) {
            await sceneManager.switchScene(sceneId);
            expect(sceneManager.getCurrentSceneId()).toBe(sceneId);
        }
    });

    test('应该正确处理并发场景操作', async () => {
        const promises = [];

        // 并发创建多个场景
        for (let i = 0; i < 5; i++) {
            promises.push(sceneManager.createScene({
                id: `concurrent-scene-${i}`,
                type: 'test'
            }, mockCanvas));
        }

        const results = await Promise.all(promises);
        expect(results).toHaveLength(5);
        expect(sceneManager.getAllScenes()).toHaveLength(5);
    });
});
```

### 性能测试

```javascript
// 性能基准测试
class SceneManagerBenchmark {
    async runBenchmarks() {
        const results = {};

        // 场景创建性能
        results.sceneCreation = await this.benchmarkSceneCreation();

        // 场景切换性能
        results.sceneSwitch = await this.benchmarkSceneSwitch();

        // 内存使用测试
        results.memoryUsage = await this.benchmarkMemoryUsage();

        return results;
    }

    async benchmarkSceneCreation() {
        const iterations = 100;
        const startTime = performance.now();

        for (let i = 0; i < iterations; i++) {
            await sceneManager.createScene({
                id: `benchmark-scene-${i}`,
                type: 'test'
            }, canvas);
        }

        const endTime = performance.now();
        return {
            totalTime: endTime - startTime,
            averageTime: (endTime - startTime) / iterations,
            iterations
        };
    }
}
```

## 总结

场景管理系统为山海经MMORPG项目提供了强大而灵活的场景管理能力。通过工厂模式、事件驱动和生命周期管理，系统实现了高效的场景切换、资源管理和性能优化。

### 核心优势

1. **模块化设计**: 清晰的职责分离，易于维护和扩展
2. **性能优化**: 内置预加载、缓存和资源管理机制
3. **事件驱动**: 松耦合的组件通信，便于调试和监控
4. **类型安全**: 完整的配置验证和错误处理
5. **可扩展性**: 支持自定义场景类型和过渡效果

### 最佳实践总结

- 始终使用异步操作处理场景创建和切换
- 实现完整的资源清理机制，避免内存泄漏
- 合理使用预加载机制，提升用户体验
- 监控性能指标，及时发现和解决问题
- 编写全面的测试，确保系统稳定性

正确使用该系统可以显著提升游戏的用户体验和开发效率，为项目的长期发展奠定坚实基础。
