// src/core/assets/AssetCache.js
// 资源缓存系统 - 实现LRU缓存策略和内存管理

/**
 * 缓存项类
 * 存储缓存的资源及其元数据
 */
class CacheItem {
    constructor(key, value, size = 0) {
        this.key = key;
        this.value = value;
        this.size = size;
        this.accessCount = 0;
        this.createdAt = Date.now();
        this.lastAccessed = Date.now();
        this.prev = null;
        this.next = null;
    }

    /**
     * 更新访问信息
     */
    updateAccess() {
        this.accessCount++;
        this.lastAccessed = Date.now();
    }

    /**
     * 获取项目年龄（毫秒）
     */
    getAge() {
        return Date.now() - this.createdAt;
    }

    /**
     * 获取自上次访问以来的时间（毫秒）
     */
    getTimeSinceLastAccess() {
        return Date.now() - this.lastAccessed;
    }
}

/**
 * 资源缓存类
 * 实现LRU（最近最少使用）缓存策略
 */
export class AssetCache {
    constructor(maxSize = 100 * 1024 * 1024) { // 默认100MB
        this.maxSize = maxSize;
        this.currentSize = 0;
        this.itemCount = 0;

        // 使用Map存储缓存项，提供O(1)查找
        this.cache = new Map();

        // 双向链表实现LRU
        this.head = new CacheItem('HEAD', null);
        this.tail = new CacheItem('TAIL', null);
        this.head.next = this.tail;
        this.tail.prev = this.head;

        // 统计信息
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalAccesses: 0,
            totalSize: 0
        };

        console.log(`资源缓存已初始化，最大大小: ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
    }

    /**
     * 设置缓存项
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} size - 项目大小（字节）
     */
    set(key, value, size = 0) {
        // 如果项目已存在，更新它
        if (this.cache.has(key)) {
            const existingItem = this.cache.get(key);
            this.currentSize -= existingItem.size;
            existingItem.value = value;
            existingItem.size = size;
            existingItem.updateAccess();
            this.currentSize += size;
            
            // 移动到链表头部
            this._moveToHead(existingItem);
            
            console.log(`更新缓存项: ${key}, 大小: ${(size / 1024).toFixed(2)}KB`);
            return;
        }

        // 创建新的缓存项
        const newItem = new CacheItem(key, value, size);
        
        // 检查是否需要清理空间
        while (this.currentSize + size > this.maxSize && this.itemCount > 0) {
            this._evictLRU();
        }

        // 如果单个项目太大，无法缓存
        if (size > this.maxSize) {
            console.warn(`项目太大无法缓存: ${key}, 大小: ${(size / 1024 / 1024).toFixed(2)}MB`);
            return;
        }

        // 添加到缓存
        this.cache.set(key, newItem);
        this.currentSize += size;
        this.itemCount++;
        this.stats.totalSize += size;

        // 添加到链表头部
        this._addToHead(newItem);

        console.log(`添加缓存项: ${key}, 大小: ${(size / 1024).toFixed(2)}KB, 总大小: ${(this.currentSize / 1024 / 1024).toFixed(2)}MB`);
    }

    /**
     * 获取缓存项
     * @param {string} key - 缓存键
     * @returns {*} 缓存值或undefined
     */
    get(key) {
        this.stats.totalAccesses++;

        if (!this.cache.has(key)) {
            this.stats.misses++;
            return undefined;
        }

        this.stats.hits++;
        const item = this.cache.get(key);
        item.updateAccess();

        // 移动到链表头部（最近使用）
        this._moveToHead(item);

        return item.value;
    }

    /**
     * 检查缓存中是否存在指定键
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        return this.cache.has(key);
    }

    /**
     * 删除缓存项
     * @param {string} key - 缓存键
     * @returns {boolean} 是否成功删除
     */
    delete(key) {
        if (!this.cache.has(key)) {
            return false;
        }

        const item = this.cache.get(key);
        this.cache.delete(key);
        this.currentSize -= item.size;
        this.itemCount--;

        // 从链表中移除
        this._removeFromList(item);

        console.log(`删除缓存项: ${key}, 释放大小: ${(item.size / 1024).toFixed(2)}KB`);
        return true;
    }

    /**
     * 清空缓存
     */
    clear() {
        // 调用所有缓存项的dispose方法
        for (const [key, item] of this.cache) {
            if (item.value && typeof item.value.dispose === 'function') {
                try {
                    item.value.dispose();
                } catch (error) {
                    console.warn(`清理缓存项时出错: ${key}`, error);
                }
            }
        }

        this.cache.clear();
        this.currentSize = 0;
        this.itemCount = 0;

        // 重置链表
        this.head.next = this.tail;
        this.tail.prev = this.head;

        console.log('缓存已清空');
    }

    /**
     * 执行LRU清理
     * @param {number} targetSize - 目标释放大小（字节）
     * @returns {number} 实际释放的大小
     */
    performLRUCleanup(targetSize = null) {
        if (targetSize === null) {
            // 默认清理25%的缓存
            targetSize = this.maxSize * 0.25;
        }

        let freedSize = 0;
        const itemsToRemove = [];

        // 从尾部开始清理（最少使用的项目）
        let current = this.tail.prev;
        while (current !== this.head && freedSize < targetSize) {
            itemsToRemove.push(current.key);
            freedSize += current.size;
            current = current.prev;
        }

        // 删除选中的项目
        itemsToRemove.forEach(key => {
            this.delete(key);
            this.stats.evictions++;
        });

        console.log(`LRU清理完成，删除 ${itemsToRemove.length} 个项目，释放 ${(freedSize / 1024 / 1024).toFixed(2)}MB`);
        return freedSize;
    }

    /**
     * 根据条件清理缓存
     * @param {Function} predicate - 判断函数，返回true的项目将被清理
     * @returns {number} 清理的项目数量
     */
    cleanupByCondition(predicate) {
        const itemsToRemove = [];

        for (const [key, item] of this.cache) {
            if (predicate(item)) {
                itemsToRemove.push(key);
            }
        }

        itemsToRemove.forEach(key => {
            this.delete(key);
            this.stats.evictions++;
        });

        console.log(`条件清理完成，删除 ${itemsToRemove.length} 个项目`);
        return itemsToRemove.length;
    }

    /**
     * 清理过期项目
     * @param {number} maxAge - 最大年龄（毫秒）
     * @returns {number} 清理的项目数量
     */
    cleanupExpired(maxAge = 30 * 60 * 1000) { // 默认30分钟
        return this.cleanupByCondition(item => item.getAge() > maxAge);
    }

    /**
     * 清理长时间未访问的项目
     * @param {number} maxIdleTime - 最大空闲时间（毫秒）
     * @returns {number} 清理的项目数量
     */
    cleanupIdle(maxIdleTime = 10 * 60 * 1000) { // 默认10分钟
        return this.cleanupByCondition(item => item.getTimeSinceLastAccess() > maxIdleTime);
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const hitRate = this.stats.totalAccesses > 0 
            ? (this.stats.hits / this.stats.totalAccesses) * 100 
            : 0;

        return {
            ...this.stats,
            hitRate: hitRate.toFixed(2) + '%',
            currentSize: this.currentSize,
            maxSize: this.maxSize,
            usagePercent: ((this.currentSize / this.maxSize) * 100).toFixed(2) + '%',
            itemCount: this.itemCount,
            averageItemSize: this.itemCount > 0 ? this.currentSize / this.itemCount : 0
        };
    }

    /**
     * 获取当前缓存大小
     * @returns {number} 当前大小（字节）
     */
    getCurrentSize() {
        return this.currentSize;
    }

    /**
     * 获取最大缓存大小
     * @returns {number} 最大大小（字节）
     */
    getMaxSize() {
        return this.maxSize;
    }

    /**
     * 设置最大缓存大小
     * @param {number} newMaxSize - 新的最大大小（字节）
     */
    setMaxSize(newMaxSize) {
        const oldMaxSize = this.maxSize;
        this.maxSize = newMaxSize;

        // 如果新大小更小，需要清理缓存
        if (newMaxSize < oldMaxSize && this.currentSize > newMaxSize) {
            const targetCleanup = this.currentSize - newMaxSize;
            this.performLRUCleanup(targetCleanup);
        }

        console.log(`缓存最大大小已更新: ${(oldMaxSize / 1024 / 1024).toFixed(2)}MB -> ${(newMaxSize / 1024 / 1024).toFixed(2)}MB`);
    }

    /**
     * 获取所有缓存键
     * @returns {Array<string>} 缓存键数组
     */
    keys() {
        return Array.from(this.cache.keys());
    }

    /**
     * 获取缓存项详细信息
     * @param {string} key - 缓存键
     * @returns {Object|null} 缓存项信息
     */
    getItemInfo(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        const item = this.cache.get(key);
        return {
            key: item.key,
            size: item.size,
            accessCount: item.accessCount,
            createdAt: item.createdAt,
            lastAccessed: item.lastAccessed,
            age: item.getAge(),
            timeSinceLastAccess: item.getTimeSinceLastAccess()
        };
    }

    /**
     * 获取所有缓存项信息
     * @returns {Array<Object>} 缓存项信息数组
     */
    getAllItemsInfo() {
        return Array.from(this.cache.keys()).map(key => this.getItemInfo(key));
    }

    // === 私有方法 ===

    /**
     * 将节点添加到链表头部
     * @private
     */
    _addToHead(node) {
        node.prev = this.head;
        node.next = this.head.next;
        this.head.next.prev = node;
        this.head.next = node;
    }

    /**
     * 从链表中移除节点
     * @private
     */
    _removeFromList(node) {
        node.prev.next = node.next;
        node.next.prev = node.prev;
    }

    /**
     * 将节点移动到链表头部
     * @private
     */
    _moveToHead(node) {
        this._removeFromList(node);
        this._addToHead(node);
    }

    /**
     * 移除并返回链表尾部节点
     * @private
     */
    _removeTail() {
        const lastNode = this.tail.prev;
        this._removeFromList(lastNode);
        return lastNode;
    }

    /**
     * 驱逐最少使用的项目
     * @private
     */
    _evictLRU() {
        const tail = this._removeTail();
        if (tail && tail.key !== 'HEAD' && tail.key !== 'TAIL') {
            // 调用dispose方法清理资源
            if (tail.value && typeof tail.value.dispose === 'function') {
                try {
                    tail.value.dispose();
                } catch (error) {
                    console.warn(`清理被驱逐的缓存项时出错: ${tail.key}`, error);
                }
            }

            this.cache.delete(tail.key);
            this.currentSize -= tail.size;
            this.itemCount--;
            this.stats.evictions++;

            console.log(`驱逐LRU项目: ${tail.key}, 释放大小: ${(tail.size / 1024).toFixed(2)}KB`);
        }
    }
}

export default AssetCache;
