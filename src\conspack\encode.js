import * as Types from './types';
import list_length_with_refs from './reftable';
import { ENCODE_EMPTY_VECTOR } from './utils';
import Headers from './headers';
import { Writer } from './io';

/** 在跨语言的数据交换中, 设置JS的数组类型的编码格式, 可以是LIST或VECTOR, 默认LIST. */
let ARRAY_ENCODED_AS = Types.LIST;

/**
 * 查看数组的编码类型, 默认是Types.LIST.
 */
function arrayEncodeType() {
    return ARRAY_ENCODED_AS;
}

/**
 * 修改数组编码格式, 参数可以是Types.LIST或Types.VECTOR.
 * 副作用: 改变ARRAY_ENCODED_AS
 * @param {symbol} encoded_as
 */
function changeArrayEncodeType(encoded_as) {
    if(encoded_as === Types.LIST || encoded_as === Types.VECTOR) {
        ARRAY_ENCODED_AS = encoded_as;
    }
    else {
        throw new Error(`Invalid array encode type ${encoded_as}, should be Types.LIST or Types.VECTOR!`);
    }
    return ARRAY_ENCODED_AS;
}

/**
 * 重置数组编码格式为Types.LIST
 * 副作用: 改动ARRAY_ENCODED_AS
 */
function resetArrayEncodeType() {
    if(ARRAY_ENCODED_AS !== Types.LIST) {
        ARRAY_ENCODED_AS = Types.LIST;
    }
    return ARRAY_ENCODED_AS;
}

/**
 *
 * @param {any} value
 * @param {Writer} writer
 * @returns {ArrayBuffer}
 */
function encode(writer, value) {
    if(writer instanceof Writer) {
        encoder_selector(value)(writer, value);
        let if_enlarge = writer.should_enlarge_buffer();
        let bytes = writer.ntake();
        if_enlarge ? writer.resize_buffer_silent() : false;  // check if should enlarge buffer
        return bytes;
    }
    else {
        return encode(new Writer(), writer);
    }
}

/**
 * 把长度数据写入缓冲
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {uint8} size_type
 * @param {uint} len
 */
function encode_length(writer, size_type, len) {
    switch (size_type) {
        case 0b00: writer.dataview_writeu8(len); break;
        case 0b01: writer.dataview_writeu16(len); break;
        case 0b10: writer.dataview_writeu32(len); break;
        default: throw new Error(`encode_length get invalid size_type ${size_type}`);
    }
}

/**
 *
 * @param {Writer} writer
 * @param {uint8} header_byte
 * @param {int} size_value
 */
function encode_header(writer, header_byte, size_value = -1) {

    let size_type = size_value !== -1 ? Headers.len_size_type(size_value) : 0x0;
    writer.dataview_writeu8(size_type | header_byte);
    if(size_value !== -1) {
        encode_length(writer, size_type, size_value);
    }
}

/**
 * Write extra info about value's type to buffer.
 * In conspack.jl, only `Dict` has properties encoded,
 * to make the byte vector decodable by lisp, (:TEST :EQUAL) will be write into the buffer.
 * 用于对象的序列化, 对应于jl的Dict, CL的hash-table
 * @param {Writer} writer
 * @param {*} value
 */
function encode_properties(writer, value) {
    encode_header(writer, Headers.PROPERTIES_HEADER);
    encoder_selector(value)(writer, value);
}

/**
 * 根据值的typeof类型, 返回其对应的编码函数.
 * 注意, 为了兼容其它语言, null和undefined都编码为布尔false
 * @param {any} value
 * @returns {function}
 */
function encoder_selector(value) {
    switch(typeof(value)) {
        case 'number':    return encode_number;    // ordinary integers and floats
        case 'string':    return encode_string;
        case 'boolean':   return encode_boolean;   // true, false
        case 'object':    return encode_object;    // null, arrays, objects
        case 'undefined': return encode_undefined; // undefined, encoded as false
        case 'symbol':    return encode_symbol;    // Symbol('abc'), encoded as CL's keyword type
        case 'bigint':    return encode_bigint;    // 1234n, up to 64 bit bigint
        default: throw new Error(`Cannot encode this value ${value} of type ${typeof(value)}.`);
    }
}

/**
 * 向缓冲写入一个布尔值
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {boolean} value
 */
function encode_boolean(writer, value) {
    // encode-boolean, bool type does not need fixed_header
    return value === true ? writer.dataview_writeu8(0b1) : writer.dataview_writeu8(0b0);
}

/**
 * 把undefined编码为false, 向缓冲写入一个false值
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {undefined} value
 */
function encode_undefined(writer, value) {
    encode_boolean(writer, false);
}

/**
 * 编码number类型, 不包括BigInt
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {number} value
 * @returns {number}
 */
function encode_number(writer, value) {
    let typeSymbol = Headers.guess_number_type(value);
    let isInteger  = Number.isInteger(value);
    encode_header(writer, Headers.number_header(typeSymbol));

    if(isInteger) {
        switch(typeSymbol) {
            case Types.INT8   : return writer.dataview_write8(value);
            case Types.UINT8  : return writer.dataview_writeu8(value);
            case Types.INT16  : return writer.dataview_write16(value);
            case Types.UINT16 : return writer.dataview_writeu16(value);
            case Types.INT32  : return writer.dataview_write32(value);
            case Types.UINT32 : return writer.dataview_writeu32(value);
            case Types.INT64  : return writer.dataview_write64(value);
            case Types.UINT64 : return writer.dataview_writeu64(value);
            case Types.INT128 : return writer.write128_be(value);
            case Types.UINT128: return writer.writeu128_be(value);
            default: {
                if(value === Number.MAX_VALUE || value === -Number.MAX_VALUE) {
                    return writer.dataview_write_double(value);
                }
                throw new Error(`Javascript supports integer types up to 128 bits, this integer type ${typeSymbol} is not supported!`);
            }
        }
    } else {
        switch(typeSymbol) {
            case Types.DOUBLE_FLOAT: return writer.dataview_write_double(value);
            case Types.SINGLE_FLOAT: return writer.dataview_write_single(value);  // cannot reach
            case Types.COMPLEX     : throw new Error(`Javascript has no COMPLEX types: ${value}.`);
            case Types.RATIONAL    : throw new Error(`Javascript has no RATIONAL types: ${value}.`);
            default: throw new Error(`This type ${typeSymbol} is not defined!`);
        }
    }
    throw new Error(`Number type not listed value: ${value} type: ${typeSymbol}.`);
}

/**
 * 编码BigInt类型, 支持到128位.
 * 副作用: 写入缓冲数据并更新缓冲索引.
 * @param {Writer} writer
 * @param {number} value
 * @returns {number}
 */
function encode_bigint(writer, value) {
    if (value >= Number.MIN_SAFE_INTEGER && value <= Number.MAX_SAFE_INTEGER) {
        return encode_number(writer, Number(value));
    } else {
        let typeSymbol = Headers.guess_number_type(value);
        let val = value;
        encode_header(writer, Headers.number_header(typeSymbol));

        switch(typeSymbol) {
            case Types.INT64  : return writer.dataview_write64(val);
            case Types.UINT64 : return writer.dataview_writeu64(val);
            case Types.INT128 : return writer.write128_be(val);
            case Types.UINT128: return writer.writeu128_be(val);
            default: throw new Error(`BigInt ${value} is supported up to 128 bit!`);
        }
    }
}

/**
 * 编码string类型.
 * 副作用: 写入缓冲数据并更新缓冲索引.
 * @param {Writer} writer
 * @param {string} value
 * @returns {Uint8Array}
 */
function encode_string(writer, value) {
    let byteArray = writer.textEncoder.encode(value);
    let ncodeunits = byteArray.byteLength;
    encode_header(writer, Headers.STRING_HEADER, ncodeunits);
    return writer.write_text_bytes(byteArray, ncodeunits);
}

/**
 * 编码symbol类型, 编码为CL的关键字类型, 为与其它语言兼容, 不允许对空描述的符号进行编码.
 * 副作用: 写入缓冲数据并更新缓冲索引.
 * @param {Writer} writer
 * @param {symbol} value
 * @returns {}
 */
function encode_symbol(writer, value) {
    if(value.description === "") {
        throw new Error('Cannot encode a symbol with empty description!');
    }
    encode_header(writer, Headers.keyword_header());
    encode_string(writer, value.description);
}

/**
 * 编码object类型
 * null编码为CL的nil
 * Array编码为CL的list或者vector
 * 其它的object编码为CL的hash-table
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {symbol} value
 * @returns {}
 */
function encode_object(writer, value) {
    if(value === null) {
        return encode_boolean(writer, false);
    }
    else if(Array.isArray(value)) {
        return encode_array(writer, value);
    }
    else {
        return encode_hashtable(writer, value);
    }
}

/**
 * Array编码为CL的list或者vector, 由encode_as参数决定
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {Array} value
 * @param {string} encode_as
 */
function encode_array(writer, value, encode_as = ARRAY_ENCODED_AS) {
    let fixed_type = false;
    if(encode_as === Types.LIST) {
        let len = list_length_with_refs(value);
        if (len < 2 && ENCODE_EMPTY_VECTOR === false) { // 空数组的len等于1, 这意味着不能对空数组进行编码
            throw new Error(`Invalid array length, len = ${value.length}, reason: Length of Array must be >= 1!`);
        }

        encode_header(writer, Headers.container_header(Types.LIST, fixed_type), len);
        if(fixed_type !== false) { encode_header(writer, fixed_type); }
        for(const val of value) {
            encoder_selector(val)(writer, val);
        }
        encode_boolean(writer, false); // Lisp的列表以nil结尾
    }
    else if(encode_as === Types.VECTOR) {
        encode_header(writer, Headers.container_header(Types.VECTOR, false), value.length);
        for(const val of value) {
            encoder_selector(val)(writer, val);
        }
    }
    else {
        throw new Error(`Invalid encode type ${encode_as} for the array.`);
    }
}

/**
 * 把对象编码为CL的哈希表
 * 副作用: 写入缓冲数据并更新缓冲索引
 * @param {Writer} writer
 * @param {object} value
 */
function encode_hashtable(writer, value) {
    if(ARRAY_ENCODED_AS !== Types.LIST) {
        throw new Error(`ARRAY_ENCODED_AS is ${ARRAY_ENCODED_AS} which should be LIST when the object is encoded as hashtable, call resetArrayEncodeType() to resore that parameter!`);
    }
    let fixed_type = false;
    let keys = Reflect.ownKeys(value); // Object.keys() will fail if there's some symbol key;

    // if extra [:TEST, :EQUAL] property for the CL's hashtable
    let has_equal_keys_p = false;
    for(let k of keys) { // 不能用for..in, in得到的是脚标
        if(typeof(k) === 'string') {
            has_equal_keys_p = true;
            break;
        }
    }
    if(has_equal_keys_p === true) {
        encode_properties(writer, [Symbol('TEST'), Symbol('EQUAL')]);
    }

    encode_header(writer, Headers.container_header(Types.MAP, fixed_type), keys.length);
    for(let k of keys) {
        encoder_selector(k)(writer, k);
        encoder_selector(value[k])(writer, value[k]);
    }
}

export { encode, arrayEncodeType, changeArrayEncodeType, resetArrayEncodeType };
