/**
 * 移动系统
 * 处理实体的移动逻辑，包括物理移动和输入处理
 * 基于bitECS 0.4.0 API
 */

import { query } from 'bitecs';
import { Transform, Physics, PlayerInput } from '../bitECSSystem.js';

/**
 * 移动系统
 * 根据速度更新实体位置，并处理玩家输入
 * @param {Object} world - ECS世界实例
 * @param {number} deltaTime - 时间增量（秒）
 */
export function movementSystem(world, deltaTime) {
    // 处理玩家输入
    const playerEntities = query(world, [PlayerInput, Transform, Physics]);
    for (const entity of playerEntities) {

        // 获取输入状态
        const moveForward = PlayerInput.moveForward[entity];
        const moveBackward = PlayerInput.moveBackward[entity];
        const moveLeft = PlayerInput.moveLeft[entity];
        const moveRight = PlayerInput.moveRight[entity];
        const jump = PlayerInput.jump[entity];

        // 计算移动方向
        let moveX = 0;
        let moveZ = 0;

        if (moveForward) moveZ += 1;
        if (moveBackward) moveZ -= 1;
        if (moveLeft) moveX -= 1;
        if (moveRight) moveX += 1;

        // 标准化移动向量
        const moveLength = Math.sqrt(moveX * moveX + moveZ * moveZ);
        if (moveLength > 0) {
            moveX /= moveLength;
            moveZ /= moveLength;
        }

        // 设置移动速度（可以根据需要调整速度值）
        const moveSpeed = 5.0; // 米/秒
        Physics.velocityX[entity] = moveX * moveSpeed;
        Physics.velocityZ[entity] = moveZ * moveSpeed;

        // 处理跳跃
        if (jump && Math.abs(Physics.velocityY[entity]) < 0.1) {
            const jumpForce = 8.0; // 跳跃力度
            Physics.velocityY[entity] = jumpForce;
        }

        // 重置输入状态（避免持续输入）
        PlayerInput.jump[entity] = 0;
    }

    // 应用物理移动
    const movableEntities = query(world, [Transform, Physics]);
    for (const entity of movableEntities) {

        // 获取当前位置和速度
        const currentX = Transform.x[entity];
        const currentY = Transform.y[entity];
        const currentZ = Transform.z[entity];

        const velocityX = Physics.velocityX[entity];
        const velocityY = Physics.velocityY[entity];
        const velocityZ = Physics.velocityZ[entity];

        // 应用重力（如果不是静态物体）
        if (!Physics.isStatic[entity]) {
            const gravity = -9.81; // 重力加速度
            Physics.velocityY[entity] += gravity * deltaTime;
        }

        // 更新位置
        Transform.x[entity] = currentX + velocityX * deltaTime;
        Transform.y[entity] = currentY + Physics.velocityY[entity] * deltaTime;
        Transform.z[entity] = currentZ + velocityZ * deltaTime;

        // 简单的地面碰撞检测
        if (Transform.y[entity] < 0) {
            Transform.y[entity] = 0;
            Physics.velocityY[entity] = 0;
        }

        // 应用摩擦力
        const friction = Physics.friction[entity] || 0.9;
        Physics.velocityX[entity] *= friction;
        Physics.velocityZ[entity] *= friction;

        // 如果速度很小，设为0以避免浮点精度问题
        if (Math.abs(Physics.velocityX[entity]) < 0.01) {
            Physics.velocityX[entity] = 0;
        }
        if (Math.abs(Physics.velocityZ[entity]) < 0.01) {
            Physics.velocityZ[entity] = 0;
        }
    }
}

/**
 * 创建玩家实体的辅助函数
 * @param {Object} ecsWorld - ECS世界实例
 * @param {number} x - 初始X位置
 * @param {number} y - 初始Y位置
 * @param {number} z - 初始Z位置
 * @returns {number} 玩家实体ID
 */
export function createPlayerEntity(ecsWorld, x = 0, y = 0, z = 0) {
    const entity = ecsWorld.createEntity();

    // 添加Transform组件
    ecsWorld.addComponent(entity, Transform, {
        x: x,
        y: y,
        z: z,
        rotationX: 0,
        rotationY: 0,
        rotationZ: 0,
        scaleX: 1,
        scaleY: 1,
        scaleZ: 1
    });

    // 添加Physics组件
    ecsWorld.addComponent(entity, Physics, {
        mass: 70.0,        // 70kg
        velocityX: 0,
        velocityY: 0,
        velocityZ: 0,
        friction: 0.8,     // 地面摩擦
        restitution: 0.1,  // 低弹性
        isKinematic: 0,
        isStatic: 0
    });

    // 添加PlayerInput组件
    ecsWorld.addComponent(entity, PlayerInput, {
        moveForward: 0,
        moveBackward: 0,
        moveLeft: 0,
        moveRight: 0,
        jump: 0,
        mouseX: 0,
        mouseY: 0
    });

    console.log(`创建玩家实体: ${entity} 位置: (${x}, ${y}, ${z})`);
    return entity;
}

/**
 * 设置玩家输入的辅助函数
 * @param {number} entity - 玩家实体ID
 * @param {Object} inputState - 输入状态对象
 */
export function setPlayerInput(entity, inputState) {
    PlayerInput.moveForward[entity] = inputState.moveForward ? 1 : 0;
    PlayerInput.moveBackward[entity] = inputState.moveBackward ? 1 : 0;
    PlayerInput.moveLeft[entity] = inputState.moveLeft ? 1 : 0;
    PlayerInput.moveRight[entity] = inputState.moveRight ? 1 : 0;
    PlayerInput.jump[entity] = inputState.jump ? 1 : 0;
    PlayerInput.mouseX[entity] = inputState.mouseX || 0;
    PlayerInput.mouseY[entity] = inputState.mouseY || 0;
}

console.log('移动系统已加载');
