// src/examples/assetManager-usage-example.js
// 资源管理系统使用示例

import { getGlobalAssetManager, AssetHelpers } from '../core/assets/index.js';

/**
 * 资源管理系统使用示例
 * 演示如何使用AssetManager加载和管理各种类型的资源
 */
export class AssetManagerExample {
    constructor() {
        this.assetManager = getGlobalAssetManager();
        this.loadedAssets = new Map();
        
        if (!this.assetManager) {
            console.error('全局资源管理器未初始化');
            return;
        }

        // 监听资源管理器事件
        this.setupEventListeners();
        
        console.log('资源管理器示例已初始化');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听资源加载开始
        this.assetManager.on('assetLoadStart', (data) => {
            console.log(`🔄 开始加载资源: ${data.url} (${data.type})`);
        });

        // 监听资源加载完成
        this.assetManager.on('assetLoaded', (data) => {
            const fromCache = data.fromCache ? '(来自缓存)' : '';
            console.log(`✅ 资源加载完成: ${data.url} ${fromCache} - 耗时: ${data.loadTime?.toFixed(2)}ms`);
        });

        // 监听资源加载失败
        this.assetManager.on('assetLoadError', (data) => {
            console.error(`❌ 资源加载失败: ${data.url}`, data.error.message);
        });

        // 监听加载进度
        this.assetManager.on('assetLoadProgress', (data) => {
            console.log(`📊 ${data.url} 加载进度: ${data.progress.toFixed(1)}%`);
        });

        // 监听垃圾回收
        this.assetManager.on('garbageCollected', (data) => {
            console.log(`🗑️ 垃圾回收完成，释放空间: ${(data.freedSpace / 1024 / 1024).toFixed(2)}MB`);
        });
    }

    /**
     * 演示配置文件加载
     */
    async demonstrateConfigLoading() {
        console.log('\n=== 配置文件加载示例 ===');
        
        try {
            // 创建示例配置数据
            const sampleConfig = {
                gameSettings: {
                    graphics: {
                        quality: 'high',
                        shadows: true,
                        antialiasing: true
                    },
                    audio: {
                        masterVolume: 0.8,
                        musicVolume: 0.6,
                        sfxVolume: 0.7
                    }
                },
                playerSettings: {
                    name: 'TestPlayer',
                    level: 1,
                    experience: 0
                }
            };

            // 模拟配置文件URL（实际项目中应该是真实的URL）
            const configUrl = 'data:application/json;base64,' + btoa(JSON.stringify(sampleConfig));
            
            const configAsset = await this.assetManager.loadConfig(configUrl);
            this.loadedAssets.set('gameConfig', configAsset);
            
            console.log('📄 配置文件内容:', configAsset.data);
            
            // 再次加载相同配置（应该从缓存获取）
            const cachedConfig = await this.assetManager.loadConfig(configUrl);
            console.log('🔄 第二次加载（来自缓存）:', cachedConfig === configAsset);
            
        } catch (error) {
            console.error('配置文件加载失败:', error);
        }
    }

    /**
     * 演示批量预加载
     */
    async demonstrateBatchPreloading() {
        console.log('\n=== 批量预加载示例 ===');
        
        // 创建示例资源列表
        const assetList = [
            {
                url: 'data:application/json;base64,' + btoa(JSON.stringify({ type: 'level1', data: 'Level 1 Data' })),
                type: 'config'
            },
            {
                url: 'data:application/json;base64,' + btoa(JSON.stringify({ type: 'level2', data: 'Level 2 Data' })),
                type: 'config'
            },
            {
                url: 'data:application/json;base64,' + btoa(JSON.stringify({ type: 'level3', data: 'Level 3 Data' })),
                type: 'config'
            }
        ];

        try {
            console.log(`📦 开始预加载 ${assetList.length} 个资源...`);
            
            const result = await this.assetManager.preloadAssets(assetList, {
                concurrent: 2, // 并发加载2个
                onProgress: (progress) => {
                    console.log(`📊 预加载进度: ${progress.completed}/${progress.total} (${(progress.progress * 100).toFixed(1)}%)`);
                }
            });

            console.log(`✅ 预加载完成: 成功 ${result.loaded.length}, 失败 ${result.failed.length}`);
            
            // 存储预加载的资源
            result.loaded.forEach((item, index) => {
                this.loadedAssets.set(`level${index + 1}`, item.asset);
            });
            
        } catch (error) {
            console.error('批量预加载失败:', error);
        }
    }

    /**
     * 演示资源缓存管理
     */
    demonstrateCacheManagement() {
        console.log('\n=== 缓存管理示例 ===');
        
        // 获取缓存统计信息
        const stats = this.assetManager.getStats();
        console.log('📊 缓存统计信息:');
        console.log(`  - 缓存大小: ${(stats.cacheSize / 1024).toFixed(2)}KB / ${(stats.cacheMaxSize / 1024 / 1024).toFixed(2)}MB`);
        console.log(`  - 缓存使用率: ${(stats.cacheUsage * 100).toFixed(2)}%`);
        console.log(`  - 缓存命中率: ${stats.cacheHits}/${stats.totalAccesses} (${((stats.cacheHits / Math.max(stats.totalAccesses, 1)) * 100).toFixed(2)}%)`);
        console.log(`  - 已注册资源: ${stats.registeredAssets}`);
        console.log(`  - 正在加载: ${stats.loadingAssets}`);

        // 获取缓存项详细信息
        const cacheInfo = this.assetManager.cache.getAllItemsInfo();
        console.log('\n📋 缓存项详情:');
        cacheInfo.forEach((item, index) => {
            console.log(`  ${index + 1}. ${item.key.substring(0, 50)}...`);
            console.log(`     大小: ${(item.size / 1024).toFixed(2)}KB, 访问次数: ${item.accessCount}`);
            console.log(`     创建时间: ${new Date(item.createdAt).toLocaleTimeString()}`);
        });
    }

    /**
     * 演示资源释放
     */
    demonstrateResourceRelease() {
        console.log('\n=== 资源释放示例 ===');
        
        // 释放特定资源
        if (this.loadedAssets.has('level1')) {
            const level1Asset = this.loadedAssets.get('level1');
            console.log('🗑️ 释放 level1 资源...');
            
            // 这里需要实际的URL来释放资源
            // this.assetManager.releaseAsset(level1Asset.url);
            this.loadedAssets.delete('level1');
        }

        // 显示释放后的统计信息
        const statsAfter = this.assetManager.getStats();
        console.log(`📊 释放后缓存大小: ${(statsAfter.cacheSize / 1024).toFixed(2)}KB`);
    }

    /**
     * 演示进度跟踪
     */
    demonstrateProgressTracking() {
        console.log('\n=== 进度跟踪示例 ===');
        
        // 获取整体加载进度
        const progress = this.assetManager.getLoadingProgress();
        console.log('📊 整体加载进度:');
        console.log(`  - 活动项目: ${progress.activeItems}`);
        console.log(`  - 活动批次: ${progress.activeBatches}`);
        console.log(`  - 总进度: ${progress.totalProgress}%`);
        console.log(`  - 正在加载: ${progress.isLoading ? '是' : '否'}`);
        
        // 显示统计信息
        console.log('\n📈 加载统计:');
        console.log(`  - 总创建: ${progress.stats.totalCreated}`);
        console.log(`  - 总完成: ${progress.stats.totalCompleted}`);
        console.log(`  - 总失败: ${progress.stats.totalFailed}`);
    }

    /**
     * 运行完整示例
     */
    async runFullExample() {
        console.log('🚀 开始资源管理器完整示例演示...\n');
        
        try {
            // 1. 配置文件加载
            await this.demonstrateConfigLoading();
            
            // 2. 批量预加载
            await this.demonstrateBatchPreloading();
            
            // 3. 缓存管理
            this.demonstrateCacheManagement();
            
            // 4. 进度跟踪
            this.demonstrateProgressTracking();
            
            // 5. 资源释放
            this.demonstrateResourceRelease();
            
            console.log('\n✅ 资源管理器示例演示完成！');
            
        } catch (error) {
            console.error('❌ 示例演示过程中发生错误:', error);
        }
    }

    /**
     * 清理示例
     */
    dispose() {
        // 清理已加载的资源
        this.loadedAssets.clear();
        
        // 移除事件监听器
        if (this.assetManager) {
            this.assetManager.removeAllListeners();
        }
        
        console.log('🧹 资源管理器示例已清理');
    }
}

/**
 * 初始化并运行资源管理器示例
 */
export async function initializeAssetManagerExample() {
    const example = new AssetManagerExample();
    
    if (example.assetManager) {
        // 运行完整示例
        await example.runFullExample();
        
        // 暴露到全局作用域供调试使用
        if (typeof window !== 'undefined') {
            window.assetManagerExample = example;
        }
        
        return example;
    } else {
        console.error('无法初始化资源管理器示例：全局资源管理器未找到');
        return null;
    }
}

/**
 * 便捷函数：演示资源加载的基本用法
 */
export async function quickAssetLoadingDemo() {
    console.log('🎯 快速资源加载演示...');
    
    try {
        // 创建示例数据
        const sampleData = { message: 'Hello from AssetManager!', timestamp: Date.now() };
        const dataUrl = 'data:application/json;base64,' + btoa(JSON.stringify(sampleData));
        
        // 使用便捷函数加载配置
        const config = await AssetHelpers.loadConfig(dataUrl);
        console.log('📄 加载的配置:', config.data);
        
        // 再次加载（应该从缓存获取）
        const cachedConfig = await AssetHelpers.loadConfig(dataUrl);
        console.log('🔄 缓存加载:', cachedConfig.data);
        
        console.log('✅ 快速演示完成！');
        
    } catch (error) {
        console.error('❌ 快速演示失败:', error);
    }
}

// 默认导出
export default AssetManagerExample;
