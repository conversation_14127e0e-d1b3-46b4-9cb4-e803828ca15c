// src/config/environment.js
// 环境系统配置文件

/**
 * 环境系统配置
 * 定义昼夜循环、天气系统、光照等环境参数
 */
export const EnvironmentConfig = {
    // 昼夜循环配置
    dayNightCycle: {
        duration: 1200000,      // 完整周期时长（毫秒）- 20分钟
        startTime: 0.25,        // 起始时间（0-1，0.25表示早晨6点）
        
        // 太阳光照强度变化
        sunIntensity: {
            dawn: 0.3,          // 黎明
            day: 1.0,           // 白天
            dusk: 0.4,          // 黄昏
            night: 0.05         // 夜晚
        },
        
        // 环境光颜色变化（RGB值0-1）
        ambientColor: {
            dawn: [0.8, 0.6, 0.4],      // 黎明 - 温暖的橙色
            day: [0.9, 0.9, 1.0],       // 白天 - 明亮的白色
            dusk: [1.0, 0.7, 0.5],      // 黄昏 - 金色
            night: [0.2, 0.2, 0.4]      // 夜晚 - 深蓝色
        },
        
        // 太阳光颜色变化
        sunColor: {
            dawn: [1.0, 0.8, 0.6],      // 黎明 - 暖橙色
            day: [1.0, 1.0, 0.9],       // 白天 - 白色
            dusk: [1.0, 0.6, 0.3],      // 黄昏 - 深橙色
            night: [0.3, 0.3, 0.5]      // 夜晚 - 月光蓝
        },
        
        // 雾效配置
        fog: {
            dawn: { density: 0.02, color: [0.8, 0.8, 0.9] },
            day: { density: 0.005, color: [0.9, 0.9, 1.0] },
            dusk: { density: 0.015, color: [1.0, 0.8, 0.6] },
            night: { density: 0.01, color: [0.3, 0.3, 0.5] }
        }
    },
    
    // 天气系统配置
    weather: {
        // 晴天
        clear: {
            visibility: 1.0,        // 能见度
            windStrength: 0.1,      // 风力强度
            cloudCover: 0.1,        // 云层覆盖度
            precipitation: 0.0,     // 降水强度
            lightModifier: 1.0,     // 光照修正系数
            fogDensity: 0.005       // 雾密度
        },
        
        // 多云
        cloudy: {
            visibility: 0.8,
            windStrength: 0.3,
            cloudCover: 0.6,
            precipitation: 0.0,
            lightModifier: 0.7,
            fogDensity: 0.01
        },
        
        // 阴天
        overcast: {
            visibility: 0.6,
            windStrength: 0.4,
            cloudCover: 0.9,
            precipitation: 0.0,
            lightModifier: 0.5,
            fogDensity: 0.02
        },
        
        // 小雨
        lightRain: {
            visibility: 0.5,
            windStrength: 0.5,
            cloudCover: 0.8,
            precipitation: 0.3,
            lightModifier: 0.4,
            fogDensity: 0.03
        },
        
        // 大雨
        heavyRain: {
            visibility: 0.3,
            windStrength: 0.8,
            cloudCover: 1.0,
            precipitation: 0.8,
            lightModifier: 0.2,
            fogDensity: 0.05
        },
        
        // 雷暴
        thunderstorm: {
            visibility: 0.2,
            windStrength: 1.0,
            cloudCover: 1.0,
            precipitation: 1.0,
            lightModifier: 0.1,
            fogDensity: 0.08,
            lightning: true
        },
        
        // 雪天
        snow: {
            visibility: 0.4,
            windStrength: 0.6,
            cloudCover: 0.9,
            precipitation: 0.5,
            lightModifier: 0.8,
            fogDensity: 0.04,
            snowfall: true
        },
        
        // 雾天
        fog: {
            visibility: 0.1,
            windStrength: 0.1,
            cloudCover: 0.7,
            precipitation: 0.0,
            lightModifier: 0.3,
            fogDensity: 0.15
        }
    },
    
    // 季节配置
    seasons: {
        spring: {
            temperatureRange: [10, 25],     // 温度范围（摄氏度）
            weatherWeights: {               // 天气出现权重
                clear: 0.4,
                cloudy: 0.3,
                lightRain: 0.2,
                overcast: 0.1
            },
            vegetationColor: [0.4, 0.8, 0.3],   // 植被颜色修正
            dayLengthModifier: 1.0              // 白天长度修正
        },
        
        summer: {
            temperatureRange: [20, 35],
            weatherWeights: {
                clear: 0.6,
                cloudy: 0.2,
                thunderstorm: 0.1,
                heavyRain: 0.1
            },
            vegetationColor: [0.2, 0.7, 0.2],
            dayLengthModifier: 1.2
        },
        
        autumn: {
            temperatureRange: [5, 20],
            weatherWeights: {
                clear: 0.3,
                cloudy: 0.4,
                overcast: 0.2,
                lightRain: 0.1
            },
            vegetationColor: [0.8, 0.6, 0.2],
            dayLengthModifier: 1.0
        },
        
        winter: {
            temperatureRange: [-10, 10],
            weatherWeights: {
                overcast: 0.4,
                snow: 0.3,
                clear: 0.2,
                fog: 0.1
            },
            vegetationColor: [0.6, 0.6, 0.6],
            dayLengthModifier: 0.8
        }
    },
    
    // 天空盒配置
    skybox: {
        // 程序化天空参数
        procedural: {
            turbidity: 10,          // 浑浊度
            rayleigh: 2,            // 瑞利散射
            mieCoefficient: 0.005,  // 米氏散射系数
            mieDirectionalG: 0.8,   // 米氏散射方向性
            luminance: 1.0,         // 亮度
            inclination: 0.49,      // 太阳倾斜度
            azimuth: 0.25          // 太阳方位角
        },
        
        // HDR环境贴图
        hdr: {
            clear: "assets/skybox/clear_sky.hdr",
            cloudy: "assets/skybox/cloudy_sky.hdr",
            overcast: "assets/skybox/overcast_sky.hdr",
            night: "assets/skybox/night_sky.hdr"
        }
    },
    
    // 山海经主题环境配置
    shanhaijing: {
        // 神秘光效配置
        mysticalEffects: {
            // 仙气效果
            celestialMist: {
                enabled: true,
                density: 0.02,
                color: [0.8, 0.9, 1.0],
                speed: 0.5,
                height: [5, 20]
            },
            
            // 灵光效果
            spiritualGlow: {
                enabled: true,
                intensity: 0.3,
                color: [0.9, 0.8, 1.0],
                pulseDuration: 5000,
                range: 50
            },
            
            // 神兽光环
            beastAura: {
                enabled: true,
                colors: {
                    dragon: [1.0, 0.8, 0.2],    // 金色
                    phoenix: [1.0, 0.3, 0.1],   // 火红色
                    qilin: [0.8, 1.0, 0.8],     // 翠绿色
                    tiger: [1.0, 1.0, 0.8]      // 银白色
                }
            }
        },
        
        // 古代时辰系统（对应现代24小时制）
        ancientTime: {
            子时: { modern: [23, 1], description: "夜半" },
            丑时: { modern: [1, 3], description: "鸡鸣" },
            寅时: { modern: [3, 5], description: "平旦" },
            卯时: { modern: [5, 7], description: "日出" },
            辰时: { modern: [7, 9], description: "食时" },
            巳时: { modern: [9, 11], description: "隅中" },
            午时: { modern: [11, 13], description: "日中" },
            未时: { modern: [13, 15], description: "日昳" },
            申时: { modern: [15, 17], description: "晡时" },
            酉时: { modern: [17, 19], description: "日入" },
            戌时: { modern: [19, 21], description: "黄昏" },
            亥时: { modern: [21, 23], description: "人定" }
        },
        
        // 五行元素环境效果
        wuxingEffects: {
            金: { color: [1.0, 0.9, 0.7], particle: "金光闪闪" },
            木: { color: [0.4, 0.8, 0.3], particle: "生机盎然" },
            水: { color: [0.3, 0.6, 1.0], particle: "水波荡漾" },
            火: { color: [1.0, 0.4, 0.2], particle: "火焰跳跃" },
            土: { color: [0.8, 0.6, 0.4], particle: "土气厚重" }
        }
    }
};

/**
 * 环境过渡配置
 * 定义环境状态之间的平滑过渡参数
 */
export const EnvironmentTransition = {
    // 昼夜过渡时长（毫秒）
    dayNightTransition: 60000,      // 1分钟
    
    // 天气过渡时长（毫秒）
    weatherTransition: 120000,      // 2分钟
    
    // 季节过渡时长（毫秒）
    seasonTransition: 300000,       // 5分钟
    
    // 插值类型
    interpolationType: "smoothstep"  // linear, smoothstep, cubic
};

/**
 * 性能优化配置
 */
export const EnvironmentPerformance = {
    // 更新频率（毫秒）
    updateInterval: 1000,           // 每秒更新一次
    
    // LOD距离设置
    lodDistances: {
        high: 100,      // 高质量效果距离
        medium: 300,    // 中等质量效果距离
        low: 500        // 低质量效果距离
    },
    
    // 粒子系统限制
    particleLimits: {
        rain: 1000,     // 雨滴粒子数量
        snow: 800,      // 雪花粒子数量
        mist: 500,      // 雾气粒子数量
        leaves: 300     // 落叶粒子数量
    }
};

export default EnvironmentConfig;
