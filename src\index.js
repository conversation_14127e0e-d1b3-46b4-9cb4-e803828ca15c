// src/index.js
// 山海经MMORPG客户端主入口文件

import {
    Engine, // 渲染引擎
} from '@babylonjs/core';
import {WebGPUEngine} from '@babylonjs/core/Engines/webgpuEngine';

import networkManager from './core/network/networkManager.js';
import SceneManager from './core/scene/SceneManager.js';
import sceneFactory from './core/scene/SceneFactory.js';
import {getDefaultSceneConfig} from './core/scene/SceneConfig.js';
import {testBitECS} from './test-bitecs.js';
import {initializeBitECSExample, healthRegenerationSystem, positionLogSystem} from './examples/bitECS-usage-example.js';
import { initializeGlobalAssetManager, AssetPresets } from './core/assets/index.js';
import { initializeAssetManagerExample } from './examples/assetManager-usage-example.js';
import { initializePhysicsExample } from './examples/physics-usage-example.js';

// 全局变量，用于存储引擎和场景管理器实例，方便其他模块访问
let engine;
let sceneManager;

/**
 * 初始化场景管理系统
 * 使用新的场景管理架构
 */
export const initializeSceneSystem = async () => {
    // 获取画布元素
    const canvas = document.getElementById("renderCanvas");
    if (!canvas) {
        console.error("未能找到ID为'renderCanvas'的canvas元素, 请检查HTML文件.");
        return null;
    }

    // 创建Babylon.js引擎
    const engineType = location.search.split("engine=")[1]?.split("&")[0] || "webgl";

    if (engineType === "webgpu") {
        const webGPUSupported = await WebGPUEngine.IsSupportedAsync;
        if (webGPUSupported) {
            // 加载WebGPU扩展
            await import("@babylonjs/core/Engines/WebGPU/Extensions/");
            const webgpu = new WebGPUEngine(canvas, {
                adaptToDeviceRatio: true,
                antialias: true,
            });
            await webgpu.initAsync();
            engine = webgpu;
            console.log("WebGPU引擎已启用");
        } else {
            // WebGPU不支持时回退到WebGL
            engine = new Engine(canvas, true, { preserveDrawingBuffer: true, stencil: true });
            console.log("WebGPU不支持，使用WebGL引擎");
        }
    } else {
        // 创建WebGL引擎
        engine = new Engine(canvas, true, { preserveDrawingBuffer: true, stencil: true });
        console.log("WebGL引擎已启用");
    }

    // 初始化资源管理系统
    console.log("正在初始化资源管理系统...");
    const assetManager = initializeGlobalAssetManager(AssetPresets.STANDARD);

    // 创建场景管理器
    sceneManager = new SceneManager(engine);

    // 注册所有场景类型到场景管理器
    console.log("注册场景类型到场景管理器...");
    const availableSceneTypes = sceneFactory.getAvailableSceneTypes();

    for (const sceneTypeInfo of availableSceneTypes) {
        // 这个factory调用的sceneFactory.createScene, 会根据type找到sceneTypes的value, 即{ name, description, factory, category },
        // 调用对象中的factory返回场景实例, options会用作创建场景的参数.
        const factory = (options) => sceneFactory.createScene(sceneTypeInfo.type, options);
        sceneManager.registerSceneType(sceneTypeInfo.type, factory);
    }

    console.log(`已注册 ${availableSceneTypes.length} 个场景类型`);

    // 获取默认场景配置并创建默认场景
    console.log("正在创建默认场景...");
    const defaultSceneConfig = getDefaultSceneConfig(); // DefaultSceneConfigs.shanhaijingWorld

    // defaultSceneConfig中的options会传给factory作为创建场景实例的参数
    const sceneData = await sceneManager.createScene(defaultSceneConfig, canvas);

    if (!sceneData) {
        console.error("默认场景创建失败");
        return null;
    }

    // 切换到默认场景
    await sceneManager.switchScene(defaultSceneConfig.id);

    // 监听窗口大小变化
    window.addEventListener("resize", () => {
        engine.resize();
    });

    // 暴露调试对象到全局作用域
    window.engine = engine;
    window.sceneManager = sceneManager;
    window.sceneFactory = sceneFactory;
    window.networkManager = networkManager;
    window.assetManager = assetManager;

    // 测试bitECS库是否正常工作
    console.log("正在测试bitECS库...");
    const bitECSTestResult = testBitECS();
    if (bitECSTestResult) {
        console.log("✅ bitECS库测试通过，ECS系统可以正常使用");
    } else {
        console.warn("⚠️ bitECS库测试失败，ECS功能可能无法正常使用");
    }

    console.log("场景系统初始化完成");
    return sceneManager;
};

/**
 * 启动游戏
 * 初始化场景系统并连接到服务器
 */
initializeSceneSystem().then((sceneManagerInstance) => {
    if (sceneManagerInstance) {
        // 连接到游戏服务器
        console.log("正在连接到游戏服务器...");
        networkManager.connect();

        console.log("🏔️ 山海经世界已成功启动！欢迎来到神秘的古代世界！");
        console.log("🎮 使用WASD移动，空格跳跃，鼠标控制视角");
        console.log("🌅 体验昼夜循环和动态天气系统");
        console.log("🐉 探索神兽出没的神秘地形");
        console.log("🔧 使用 window.sceneManager 访问场景管理系统");

        // 初始化bitECS示例
        console.log("🎯 正在初始化bitECS示例系统...");
        try {
            const ecsEntities = initializeBitECSExample();

            // 注册额外的系统
            import('./core/ecs/bitECSSystem.js').then(({ ecsWorld }) => {
                ecsWorld.registerSystem(healthRegenerationSystem, 'HealthRegenerationSystem');
                ecsWorld.registerSystem(positionLogSystem, 'PositionLogSystem');
                console.log("✅ 额外ECS系统已注册");
            });

            console.log("✅ bitECS示例系统初始化完成");
            console.log("🎮 现在可以使用WASD键控制ECS玩家实体移动");

            // 暴露ECS相关对象到全局作用域
            window.ecsEntities = ecsEntities;

        } catch (error) {
            console.warn("⚠️ bitECS示例初始化失败:", error);
        }

        // 初始化资源管理器示例
        console.log("🎯 正在初始化资源管理器示例...");
        initializeAssetManagerExample().then(assetExample => {
            if (assetExample) {
                console.log("✅ 资源管理器示例初始化完成");
                console.log("🔧 使用 window.assetManagerExample 访问资源管理器示例");
                window.assetManagerExample = assetExample;
            }
        }).catch(error => {
            console.warn("⚠️ 资源管理器示例初始化失败:", error);
        });

        // 初始化物理系统示例
        console.log("🎯 正在初始化物理系统示例...");
        const currentScene = sceneManagerInstance.getCurrentScene();
        if (currentScene && currentScene.babylonScene) {
            initializePhysicsExample(currentScene.babylonScene, sceneManagerInstance).then(physicsExample => {
                if (physicsExample) {
                    console.log("✅ 物理系统示例初始化完成");
                    console.log("🔧 使用 window.physicsExample 访问物理系统示例");
                    window.physicsExample = physicsExample;
                }
            }).catch(error => {
                console.warn("⚠️ 物理系统示例初始化失败:", error);
            });
        } else {
            console.warn("⚠️ 当前没有活动场景，跳过物理系统示例初始化");
        }

        // 设置场景管理器事件监听
        sceneManagerInstance.on('sceneTransitionComplete', (data) => {
            console.log(`场景切换完成: ${data.from} -> ${data.to}`);

            // 场景切换后重新初始化物理示例
            const newScene = sceneManagerInstance.getCurrentScene();
            if (newScene && newScene.babylonScene) {
                initializePhysicsExample(newScene.babylonScene, sceneManagerInstance).then(physicsExample => {
                    if (physicsExample) {
                        console.log("✅ 新场景物理系统示例已初始化");
                        window.physicsExample = physicsExample;
                    }
                }).catch(error => {
                    console.warn("⚠️ 新场景物理系统示例初始化失败:", error);
                });
            }
        });

        sceneManagerInstance.on('sceneCreated', (data) => {
            console.log(`新场景已创建: ${data.id} (${data.type})`);
        });
    }
}).catch(error => {
    console.error("游戏启动失败:", error);

    // 显示错误信息给用户
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 10px;
        font-family: Arial, sans-serif;
        text-align: center;
        z-index: 1000;
    `;
    errorDiv.innerHTML = `
        <h3>游戏启动失败</h3>
        <p>请检查浏览器控制台获取详细错误信息</p>
        <p>错误: ${error.message}</p>
        <button onclick="location.reload()">重新加载</button>
    `;
    document.body.appendChild(errorDiv);
});
