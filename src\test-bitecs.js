/**
 * bitECS库测试文件
 * 用于验证bitECS库是否能正常导入和使用
 * 基于bitECS 0.4.0 API
 */

// 导入bitECS核心功能
import {
    createWorld,
    addEntity,
    addComponent,
    hasComponent,
    query
} from 'bitecs';

/**
 * 测试bitECS基本功能
 */
export function testBitECS() {
    console.log('开始测试bitECS库...');

    try {
        // 创建世界
        const world = createWorld();
        console.log('✓ 成功创建世界');

        // 定义组件 - bitECS 0.4.0使用SoA格式
        const Position = {
            x: [],
            y: [],
            z: []
        };

        const Velocity = {
            x: [],
            y: [],
            z: []
        };

        console.log('✓ 成功定义组件');

        // 创建实体
        const entity = addEntity(world);
        console.log(`✓ 成功创建实体，ID: ${entity}`);

        // 添加组件到实体
        addComponent(world, entity, Position);
        addComponent(world, entity, Velocity);

        // 设置组件数据
        Position.x[entity] = 10.0;
        Position.y[entity] = 20.0;
        Position.z[entity] = 30.0;

        Velocity.x[entity] = 1.0;
        Velocity.y[entity] = 0.5;
        Velocity.z[entity] = 0.0;

        console.log('✓ 成功添加组件并设置数据');
        console.log(`实体位置: (${Position.x[entity]}, ${Position.y[entity]}, ${Position.z[entity]})`);
        console.log(`实体速度: (${Velocity.x[entity]}, ${Velocity.y[entity]}, ${Velocity.z[entity]})`);

        // 检查组件
        const hasPos = hasComponent(world, entity, Position);
        const hasVel = hasComponent(world, entity, Velocity);
        console.log(`✓ 实体组件检查 - Position: ${hasPos}, Velocity: ${hasVel}`);

        // 创建查询
        const entities = query(world, [Position, Velocity]);
        console.log(`✓ 查询到 ${entities.length} 个可移动实体`);

        // 简单的移动系统测试
        for (const eid of entities) {
            Position.x[eid] += Velocity.x[eid];
            Position.y[eid] += Velocity.y[eid];
            Position.z[eid] += Velocity.z[eid];
        }

        console.log('✓ 成功执行移动系统');
        console.log(`更新后位置: (${Position.x[entity]}, ${Position.y[entity]}, ${Position.z[entity]})`);

        console.log('🎉 bitECS库测试完成，所有功能正常！');
        return true;

    } catch (error) {
        console.error('❌ bitECS库测试失败:', error);
        return false;
    }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.testBitECS = testBitECS;
    console.log('bitECS测试函数已添加到window对象，可通过window.testBitECS()调用');
}
