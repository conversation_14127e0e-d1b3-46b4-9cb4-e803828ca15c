// src/core/assets/LoadingProgressManager.js
// 加载进度管理器 - 跟踪单个和批量资源的加载进度

/**
 * 进度状态枚举
 */
export const ProgressStatus = {
    PENDING: 'pending',
    LOADING: 'loading',
    COMPLETED: 'completed',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};

/**
 * 单个进度项类
 */
class ProgressItem {
    constructor(id, url, type, totalSize = 0) {
        this.id = id;
        this.url = url;
        this.type = type;
        this.status = ProgressStatus.PENDING;
        this.progress = 0; // 0-100
        this.loadedSize = 0;
        this.totalSize = totalSize;
        this.startTime = null;
        this.endTime = null;
        this.error = null;
        this.metadata = {};
    }

    /**
     * 开始加载
     */
    start() {
        this.status = ProgressStatus.LOADING;
        this.startTime = Date.now();
        this.progress = 0;
    }

    /**
     * 更新进度
     * @param {number} progress - 进度百分比 (0-100)
     * @param {number} loadedSize - 已加载大小（字节）
     */
    updateProgress(progress, loadedSize = 0) {
        this.progress = Math.max(0, Math.min(100, progress));
        this.loadedSize = loadedSize;
        
        if (this.status === ProgressStatus.PENDING) {
            this.start();
        }
    }

    /**
     * 完成加载
     */
    complete() {
        this.status = ProgressStatus.COMPLETED;
        this.progress = 100;
        this.endTime = Date.now();
    }

    /**
     * 加载失败
     * @param {Error} error - 错误对象
     */
    fail(error) {
        this.status = ProgressStatus.FAILED;
        this.error = error;
        this.endTime = Date.now();
    }

    /**
     * 取消加载
     */
    cancel() {
        this.status = ProgressStatus.CANCELLED;
        this.endTime = Date.now();
    }

    /**
     * 获取加载时间（毫秒）
     */
    getLoadTime() {
        if (!this.startTime) return 0;
        const endTime = this.endTime || Date.now();
        return endTime - this.startTime;
    }

    /**
     * 获取加载速度（字节/秒）
     */
    getLoadSpeed() {
        const loadTime = this.getLoadTime();
        if (loadTime === 0 || this.loadedSize === 0) return 0;
        return (this.loadedSize / loadTime) * 1000; // 转换为字节/秒
    }

    /**
     * 估算剩余时间（毫秒）
     */
    getEstimatedTimeRemaining() {
        if (this.progress === 0 || this.progress >= 100) return 0;
        
        const loadTime = this.getLoadTime();
        if (loadTime === 0) return 0;
        
        const remainingProgress = 100 - this.progress;
        const timePerPercent = loadTime / this.progress;
        return remainingProgress * timePerPercent;
    }

    /**
     * 获取进度信息
     */
    getInfo() {
        return {
            id: this.id,
            url: this.url,
            type: this.type,
            status: this.status,
            progress: this.progress,
            loadedSize: this.loadedSize,
            totalSize: this.totalSize,
            loadTime: this.getLoadTime(),
            loadSpeed: this.getLoadSpeed(),
            estimatedTimeRemaining: this.getEstimatedTimeRemaining(),
            error: this.error ? this.error.message : null,
            metadata: this.metadata
        };
    }
}

/**
 * 批量进度项类
 */
class BatchProgressItem {
    constructor(id, items = []) {
        this.id = id;
        this.items = items; // 资源信息数组
        this.status = ProgressStatus.PENDING;
        this.progress = 0;
        this.completedCount = 0;
        this.failedCount = 0;
        this.totalCount = items.length;
        this.startTime = null;
        this.endTime = null;
        this.individualProgress = new Map(); // 存储每个项目的进度
    }

    /**
     * 开始批量加载
     */
    start() {
        this.status = ProgressStatus.LOADING;
        this.startTime = Date.now();
    }

    /**
     * 更新整体进度
     * @param {number} progress - 进度百分比 (0-100)
     */
    updateProgress(progress) {
        this.progress = Math.max(0, Math.min(100, progress));
        
        if (this.status === ProgressStatus.PENDING) {
            this.start();
        }
    }

    /**
     * 更新单个项目进度
     * @param {string} itemId - 项目ID
     * @param {number} progress - 进度百分比
     */
    updateItemProgress(itemId, progress) {
        this.individualProgress.set(itemId, progress);
        
        // 计算整体进度
        const totalProgress = Array.from(this.individualProgress.values())
            .reduce((sum, p) => sum + p, 0);
        const averageProgress = this.totalCount > 0 ? totalProgress / this.totalCount : 0;
        
        this.updateProgress(averageProgress);
    }

    /**
     * 标记项目完成
     * @param {string} itemId - 项目ID
     */
    completeItem(itemId) {
        this.updateItemProgress(itemId, 100);
        this.completedCount++;
        
        if (this.completedCount + this.failedCount >= this.totalCount) {
            this.complete();
        }
    }

    /**
     * 标记项目失败
     * @param {string} itemId - 项目ID
     */
    failItem(itemId) {
        this.failedCount++;
        
        if (this.completedCount + this.failedCount >= this.totalCount) {
            this.complete();
        }
    }

    /**
     * 完成批量加载
     */
    complete() {
        this.status = ProgressStatus.COMPLETED;
        this.progress = 100;
        this.endTime = Date.now();
    }

    /**
     * 批量加载失败
     * @param {Error} error - 错误对象
     */
    fail(error) {
        this.status = ProgressStatus.FAILED;
        this.error = error;
        this.endTime = Date.now();
    }

    /**
     * 获取批量进度信息
     */
    getInfo() {
        return {
            id: this.id,
            status: this.status,
            progress: this.progress,
            completedCount: this.completedCount,
            failedCount: this.failedCount,
            totalCount: this.totalCount,
            loadTime: this.getLoadTime(),
            individualProgress: Object.fromEntries(this.individualProgress)
        };
    }

    /**
     * 获取加载时间
     */
    getLoadTime() {
        if (!this.startTime) return 0;
        const endTime = this.endTime || Date.now();
        return endTime - this.startTime;
    }
}

/**
 * 加载进度管理器类
 */
export class LoadingProgressManager {
    constructor() {
        // 单个进度项存储
        this.progressItems = new Map();
        
        // 批量进度项存储
        this.batchProgressItems = new Map();
        
        // ID生成器
        this.nextId = 1;
        
        // 统计信息
        this.stats = {
            totalCreated: 0,
            totalCompleted: 0,
            totalFailed: 0,
            totalCancelled: 0
        };

        console.log('加载进度管理器已初始化');
    }

    /**
     * 创建单个进度跟踪
     * @param {string} url - 资源URL
     * @param {string} type - 资源类型
     * @param {number} totalSize - 总大小（字节）
     * @returns {string} 进度ID
     */
    createProgress(url, type, totalSize = 0) {
        const id = `progress_${this.nextId++}`;
        const progressItem = new ProgressItem(id, url, type, totalSize);
        
        this.progressItems.set(id, progressItem);
        this.stats.totalCreated++;
        
        console.log(`创建进度跟踪: ${id} (${url})`);
        return id;
    }

    /**
     * 更新进度
     * @param {string} progressId - 进度ID
     * @param {number} progress - 进度百分比 (0-100)
     * @param {number} loadedSize - 已加载大小（字节）
     */
    updateProgress(progressId, progress, loadedSize = 0) {
        const progressItem = this.progressItems.get(progressId);
        if (!progressItem) {
            console.warn(`进度项不存在: ${progressId}`);
            return;
        }

        progressItem.updateProgress(progress, loadedSize);
    }

    /**
     * 完成进度
     * @param {string} progressId - 进度ID
     */
    completeProgress(progressId) {
        const progressItem = this.progressItems.get(progressId);
        if (!progressItem) {
            console.warn(`进度项不存在: ${progressId}`);
            return;
        }

        progressItem.complete();
        this.stats.totalCompleted++;
        
        console.log(`进度完成: ${progressId} (${progressItem.url})`);
    }

    /**
     * 标记进度失败
     * @param {string} progressId - 进度ID
     * @param {Error} error - 错误对象
     */
    failProgress(progressId, error) {
        const progressItem = this.progressItems.get(progressId);
        if (!progressItem) {
            console.warn(`进度项不存在: ${progressId}`);
            return;
        }

        progressItem.fail(error);
        this.stats.totalFailed++;
        
        console.log(`进度失败: ${progressId} (${progressItem.url})`, error.message);
    }

    /**
     * 取消进度
     * @param {string} progressId - 进度ID
     */
    cancelProgress(progressId) {
        const progressItem = this.progressItems.get(progressId);
        if (!progressItem) {
            console.warn(`进度项不存在: ${progressId}`);
            return;
        }

        progressItem.cancel();
        this.stats.totalCancelled++;
        
        console.log(`进度取消: ${progressId} (${progressItem.url})`);
    }

    /**
     * 创建批量进度跟踪
     * @param {Array} items - 资源项数组
     * @returns {string} 批量进度ID
     */
    createBatchProgress(items) {
        const id = `batch_${this.nextId++}`;
        const batchProgressItem = new BatchProgressItem(id, items);
        
        this.batchProgressItems.set(id, batchProgressItem);
        
        console.log(`创建批量进度跟踪: ${id} (${items.length} 个项目)`);
        return id;
    }

    /**
     * 更新批量进度
     * @param {string} batchId - 批量进度ID
     * @param {number} progress - 进度百分比 (0-100)
     */
    updateBatchProgress(batchId, progress) {
        const batchItem = this.batchProgressItems.get(batchId);
        if (!batchItem) {
            console.warn(`批量进度项不存在: ${batchId}`);
            return;
        }

        batchItem.updateProgress(progress);
    }

    /**
     * 更新批量中单个项目的进度
     * @param {string} batchId - 批量进度ID
     * @param {string} itemId - 项目ID
     * @param {number} progress - 进度百分比
     */
    updateBatchItemProgress(batchId, itemId, progress) {
        const batchItem = this.batchProgressItems.get(batchId);
        if (!batchItem) {
            console.warn(`批量进度项不存在: ${batchId}`);
            return;
        }

        batchItem.updateItemProgress(itemId, progress);
    }

    /**
     * 完成批量进度
     * @param {string} batchId - 批量进度ID
     */
    completeBatchProgress(batchId) {
        const batchItem = this.batchProgressItems.get(batchId);
        if (!batchItem) {
            console.warn(`批量进度项不存在: ${batchId}`);
            return;
        }

        batchItem.complete();
        console.log(`批量进度完成: ${batchId}`);
    }

    /**
     * 标记批量进度失败
     * @param {string} batchId - 批量进度ID
     * @param {Error} error - 错误对象
     */
    failBatchProgress(batchId, error) {
        const batchItem = this.batchProgressItems.get(batchId);
        if (!batchItem) {
            console.warn(`批量进度项不存在: ${batchId}`);
            return;
        }

        batchItem.fail(error);
        console.log(`批量进度失败: ${batchId}`, error.message);
    }

    /**
     * 获取单个进度信息
     * @param {string} progressId - 进度ID
     * @returns {Object|null} 进度信息
     */
    getProgress(progressId) {
        const progressItem = this.progressItems.get(progressId);
        return progressItem ? progressItem.getInfo() : null;
    }

    /**
     * 获取批量进度信息
     * @param {string} batchId - 批量进度ID
     * @returns {Object|null} 批量进度信息
     */
    getBatchProgress(batchId) {
        const batchItem = this.batchProgressItems.get(batchId);
        return batchItem ? batchItem.getInfo() : null;
    }

    /**
     * 获取所有进度信息
     * @returns {Object} 所有进度信息
     */
    getAllProgress() {
        const individual = {};
        const batch = {};

        for (const [id, item] of this.progressItems) {
            individual[id] = item.getInfo();
        }

        for (const [id, item] of this.batchProgressItems) {
            batch[id] = item.getInfo();
        }

        return { individual, batch };
    }

    /**
     * 获取整体进度概览
     * @returns {Object} 整体进度信息
     */
    getOverallProgress() {
        const activeItems = Array.from(this.progressItems.values())
            .filter(item => item.status === ProgressStatus.LOADING);
        
        const activeBatches = Array.from(this.batchProgressItems.values())
            .filter(item => item.status === ProgressStatus.LOADING);

        const totalProgress = activeItems.length > 0 
            ? activeItems.reduce((sum, item) => sum + item.progress, 0) / activeItems.length
            : 100;

        return {
            activeItems: activeItems.length,
            activeBatches: activeBatches.length,
            totalProgress: totalProgress.toFixed(2),
            stats: this.stats,
            isLoading: activeItems.length > 0 || activeBatches.length > 0
        };
    }

    /**
     * 清理已完成的进度项
     * @param {number} maxAge - 最大保留时间（毫秒）
     */
    cleanup(maxAge = 5 * 60 * 1000) { // 默认5分钟
        const now = Date.now();
        let cleanedCount = 0;

        // 清理单个进度项
        for (const [id, item] of this.progressItems) {
            if (item.endTime && (now - item.endTime) > maxAge) {
                this.progressItems.delete(id);
                cleanedCount++;
            }
        }

        // 清理批量进度项
        for (const [id, item] of this.batchProgressItems) {
            if (item.endTime && (now - item.endTime) > maxAge) {
                this.batchProgressItems.delete(id);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期的进度项`);
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            activeItems: this.progressItems.size,
            activeBatches: this.batchProgressItems.size
        };
    }

    /**
     * 清理所有进度项
     */
    dispose() {
        this.progressItems.clear();
        this.batchProgressItems.clear();
        console.log('加载进度管理器已清理');
    }
}

export default LoadingProgressManager;
