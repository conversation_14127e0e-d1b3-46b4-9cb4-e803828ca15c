// src/core/SceneManager.unit.test.js
// 场景管理系统单元测试

import SceneManager from './SceneManager.js';
import { getDefaultSceneConfig } from './SceneConfig.js';

// 模拟场景工厂，避免导入Babylon.js依赖
const mockSceneFactory = {
    getAvailableSceneTypes: jest.fn(() => [
        { type: 'test', name: '测试场景', category: 'test' },
        { type: 'shanhaijing', name: '山海经场景', category: 'game' }
    ]),
    createScene: jest.fn((type) => ({
        createScene: jest.fn().mockResolvedValue({
            render: jest.fn(),
            dispose: jest.fn()
        }),
        dispose: jest.fn()
    }))
};

// 模拟Babylon.js引擎
const mockEngine = {
    runRenderLoop: jest.fn(),
    resize: jest.fn(),
    getDeltaTime: jest.fn(() => 16.67)
};

// 模拟画布
const mockCanvas = {
    width: 800,
    height: 600
};

// 模拟场景实例
const mockSceneInstance = {
    createScene: jest.fn().mockResolvedValue({
        render: jest.fn(),
        dispose: jest.fn()
    }),
    dispose: jest.fn(),
    pause: jest.fn(),
    resume: jest.fn()
};

describe('SceneManager', () => {
    let sceneManager;

    beforeEach(() => {
        sceneManager = new SceneManager(mockEngine);
        jest.clearAllMocks();
    });

    afterEach(() => {
        if (sceneManager) {
            sceneManager.dispose();
        }
    });

    describe('场景类型注册', () => {
        test('应该能够注册场景类型', () => {
            const factory = jest.fn(() => mockSceneInstance);

            sceneManager.registerSceneType('test', factory);

            expect(sceneManager.getRegisteredSceneTypes()).toContain('test');
        });

        test('注册非函数工厂应该抛出错误', () => {
            expect(() => {
                sceneManager.registerSceneType('invalid', 'not-a-function');
            }).toThrow('场景工厂必须是函数');
        });
    });

    describe('场景创建', () => {
        beforeEach(() => {
            const factory = jest.fn(() => mockSceneInstance);
            sceneManager.registerSceneType('test', factory);
        });

        test('应该能够创建场景', async () => {
            const config = {
                id: 'test-scene',
                type: 'test',
                options: { testOption: true }
            };

            const sceneData = await sceneManager.createScene(config, mockCanvas);

            expect(sceneData).toBeDefined();
            expect(sceneData.id).toBe('test-scene');
            expect(sceneData.type).toBe('test');
            expect(mockSceneInstance.createScene).toHaveBeenCalledWith(mockEngine, mockCanvas);
        });

        test('创建重复场景应该返回现有实例', async () => {
            const config = {
                id: 'test-scene',
                type: 'test'
            };

            const sceneData1 = await sceneManager.createScene(config, mockCanvas);
            const sceneData2 = await sceneManager.createScene(config, mockCanvas);

            expect(sceneData1).toBe(sceneData2);
            expect(mockSceneInstance.createScene).toHaveBeenCalledTimes(1);
        });

        test('创建未注册类型的场景应该抛出错误', async () => {
            const config = {
                id: 'test-scene',
                type: 'unregistered'
            };

            await expect(sceneManager.createScene(config, mockCanvas))
                .rejects.toThrow('未注册的场景类型: unregistered');
        });

        test('配置缺少必需字段应该抛出错误', async () => {
            const config = {
                id: 'test-scene'
                // 缺少type字段
            };

            await expect(sceneManager.createScene(config, mockCanvas))
                .rejects.toThrow('场景配置必须包含id和type字段');
        });
    });

    describe('场景切换', () => {
        beforeEach(async () => {
            const factory = jest.fn(() => mockSceneInstance);
            sceneManager.registerSceneType('test', factory);

            // 创建两个测试场景
            await sceneManager.createScene({
                id: 'scene1',
                type: 'test'
            }, mockCanvas);

            await sceneManager.createScene({
                id: 'scene2',
                type: 'test'
            }, mockCanvas);
        });

        test('应该能够切换场景', async () => {
            await sceneManager.switchScene('scene1');
            expect(sceneManager.getCurrentSceneId()).toBe('scene1');

            await sceneManager.switchScene('scene2');
            expect(sceneManager.getCurrentSceneId()).toBe('scene2');
        });

        test('切换到不存在的场景应该抛出错误', async () => {
            await expect(sceneManager.switchScene('nonexistent'))
                .rejects.toThrow('场景不存在: nonexistent');
        });

        test('切换到当前场景应该直接返回', async () => {
            await sceneManager.switchScene('scene1');
            const result = await sceneManager.switchScene('scene1');

            expect(result.id).toBe('scene1');
        });
    });

    describe('场景管理', () => {
        beforeEach(async () => {
            const factory = jest.fn(() => mockSceneInstance);
            sceneManager.registerSceneType('test', factory);

            await sceneManager.createScene({
                id: 'test-scene',
                type: 'test'
            }, mockCanvas);
        });

        test('应该能够检查场景是否存在', () => {
            expect(sceneManager.hasScene('test-scene')).toBe(true);
            expect(sceneManager.hasScene('nonexistent')).toBe(false);
        });

        test('应该能够获取场景信息', () => {
            const info = sceneManager.getSceneInfo('test-scene');

            expect(info).toBeDefined();
            expect(info.id).toBe('test-scene');
            expect(info.type).toBe('test');
        });

        test('应该能够获取所有场景', () => {
            const scenes = sceneManager.getAllScenes();

            expect(scenes).toHaveLength(1);
            expect(scenes[0].id).toBe('test-scene');
        });

        test('应该能够销毁场景', () => {
            sceneManager.disposeScene('test-scene');

            expect(sceneManager.hasScene('test-scene')).toBe(false);
            expect(mockSceneInstance.dispose).toHaveBeenCalled();
        });
    });

    describe('性能统计', () => {
        test('应该能够获取性能统计信息', () => {
            const stats = sceneManager.getPerformanceStats();

            expect(stats).toBeDefined();
            expect(stats.totalScenes).toBe(0);
            expect(stats.currentSceneId).toBeNull();
        });
    });
});

describe('MockSceneFactory', () => {
    test('应该能够获取可用的场景类型', () => {
        const types = mockSceneFactory.getAvailableSceneTypes();

        expect(types).toBeInstanceOf(Array);
        expect(types.length).toBeGreaterThan(0);

        // 检查是否包含预期的场景类型
        const typeNames = types.map(t => t.type);
        expect(typeNames).toContain('shanhaijing');
        expect(typeNames).toContain('test');
    });

    test('应该能够创建场景实例', () => {
        const instance = mockSceneFactory.createScene('test');

        expect(instance).toBeDefined();
        expect(typeof instance.createScene).toBe('function');
    });
});

describe('SceneConfig', () => {
    test('应该能够获取默认场景配置', () => {
        const config = getDefaultSceneConfig();

        expect(config).toBeDefined();
        expect(config.id).toBeDefined();
        expect(config.type).toBeDefined();
        expect(config.isDefault).toBe(true);
    });
});
