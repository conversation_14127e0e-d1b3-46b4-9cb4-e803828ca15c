import Headers from "./headers";

let CONSPACK_SECURITY = false;
let CONSPACK_MAX_BYTES = false;
let CONSPACK_FORWARD_REFS = true;
let BYTES_ALLOC = false;

const PLATFORM_BYTES = 8;

class Secure {
    /** Return an array to hold the global vars temporarily */
    static #cacheGlobal() {
        return [CONSPACK_SECURITY, CONSPACK_MAX_BYTES, CONSPACK_FORWARD_REFS, BYTES_ALLOC];
    }

    /** Restore the global vars to their previous values.
     * @param {any[]} cache
     */
    static #restoreGlobal(cache) {
        // [CONSPACK_SECURITY, CONSPACK_MAX_BYTES, CONSPACK_FORWARD_REFS, BYTES_ALLOC] = cache; // nodejs does not support this!
        CONSPACK_SECURITY = cache[0];
        CONSPACK_MAX_BYTES = cache[1];
        CONSPACK_FORWARD_REFS = cache[2];
        BYTES_ALLOC = cache[3];
    }

    /*
    (defmacro with-conspack-security ((&key (max-bytes nil) (forward-refs t))
                                      &body body)
      `(let ((*conspack-security* t)
             (*conspack-max-bytes* ,max-bytes)
             (*conspack-forward-refs* ,forward-refs)
             (*bytes-alloc* (or *bytes-alloc* 0)))
         ,@body))
    */
    static with_conspack_security(maxBytes, forwardRefs, bodyFn) {
        let globalVars = this.#cacheGlobal();
        CONSPACK_SECURITY = true;
        CONSPACK_MAX_BYTES = maxBytes;
        CONSPACK_FORWARD_REFS = forwardRefs;
        BYTES_ALLOC = BYTES_ALLOC || 0;
        let res = bodyFn();
        this.#restoreGlobal(globalVars);
        return res;
    }

    /*
    (defun use-bytes (n &optional (times 1))
      (when *conspack-max-bytes*
        (incf *bytes-alloc* (* n times))
        (when (> *bytes-alloc* *conspack-max-bytes*)
          (error 'max-size-exceeded
                 :value *bytes-alloc*
                 :reason "Size restricted."))))
    */
    static use_bytes(n, times = 1) {
        if (CONSPACK_MAX_BYTES !== false) {
            BYTES_ALLOC += n * times;
            if (BYTES_ALLOC > CONSPACK_MAX_BYTES) {
                throw new Error(`max-size-exceeded, value: ${BYTES_ALLOC} reason: Size restricted.`);
            }
        }
    }

    /*
    (defun precheck-bytes (n &optional (times 1))
      (unless *conspack-max-bytes* (return-from precheck-bytes))
      (when (> (+ (* n times) *bytes-alloc*)
               *conspack-max-bytes*)
        (error 'max-size-exceeded
               :value (+ (* n times) *bytes-alloc*)
               :reason "Size restricted.")))
    */
    static precheck_bytes(n, times = 1) {
        if (CONSPACK_MAX_BYTES === false) { return false; }
        if (n * times + BYTES_ALLOC > CONSPACK_MAX_BYTES) {
            throw new Error(`max-size-exceeded, value: ${BYTES_ALLOC + n * times}, reason: Size restricted.`);
        }
    }

    /*
    (defun container-precheck-bytes (len fixed-header)
      (when *conspack-max-bytes*
        (use-bytes +platform-bytes+)
        (if (and fixed-header (number-p fixed-header))
            (precheck-bytes (number-size fixed-header) len)
            (precheck-bytes +platform-bytes+ len))))
    */
   /**
    *
    * @param {uint} len
    * @param {uint8 | false} fixedHeader
    */
    static container_precheck_bytes(len, fixedHeader) {
        if (CONSPACK_MAX_BYTES) {
            this.use_bytes(PLATFORM_BYTES);
            if (fixedHeader !== false && !isNaN(fixedHeader)) {
                this.precheck_bytes(Headers.number_size(fixedHeader), len);
            }
            else {
                this.precheck_bytes(PLATFORM_BYTES, len);
            }
        }
    }
}

export default Secure;
export { PLATFORM_BYTES };
