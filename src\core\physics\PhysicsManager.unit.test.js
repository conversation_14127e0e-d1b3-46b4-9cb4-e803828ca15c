/**
 * PhysicsManager 单元测试
 * 测试物理系统管理器的所有核心功能
 */

import { jest } from '@jest/globals';
import { Vector3, Quaternion } from "@babylonjs/core/Maths/math.vector";
import { Scene } from "@babylonjs/core/scene";
import { Engine } from "@babylonjs/core";
import { PhysicsManager, PhysicsMaterialPresets, PhysicsShapeTypes } from './PhysicsManager.js';

// Mock Babylon.js 相关模块
jest.mock('@babylonjs/havok', () => {
    return jest.fn(() => Promise.resolve({
        // Mock Havok instance
        createWorld: jest.fn(),
        setGravity: jest.fn()
    }));
});

jest.mock('@babylonjs/core/Physics/v2/Plugins/havokPlugin', () => {
    return {
        HavokPlugin: jest.fn().mockImplementation(() => ({
            setEnabled: jest.fn(),
            setDebugMode: jest.fn()
        }))
    };
});

// Mock Scene 和 Engine
const mockEngine = {
    dispose: jest.fn()
};

const mockScene = {
    enablePhysics: jest.fn(),
    disablePhysicsEngine: jest.fn(),
    getPhysicsEngine: jest.fn(() => ({
        setGravity: jest.fn(),
        setSubTimeStep: jest.fn(),
        setMaxSubSteps: jest.fn(),
        setEnabled: jest.fn(),
        setDebugMode: jest.fn(),
        raycast: jest.fn(() => ({
            hasHit: false,
            body: null,
            point: Vector3.Zero(),
            normal: Vector3.Up()
        }))
    })),
    dispose: jest.fn()
};

describe('PhysicsManager', () => {
    let physicsManager;

    beforeEach(() => {
        // 重置所有 mock
        jest.clearAllMocks();
        
        // 创建新的 PhysicsManager 实例
        physicsManager = new PhysicsManager(mockScene);
    });

    afterEach(() => {
        if (physicsManager) {
            physicsManager.dispose();
        }
    });

    describe('构造函数', () => {
        test('应该正确初始化PhysicsManager', () => {
            expect(physicsManager.scene).toBe(mockScene);
            expect(physicsManager.isInitialized).toBe(false);
            expect(physicsManager.physicsBodies).toBeInstanceOf(Map);
            expect(physicsManager.triggers).toBeInstanceOf(Map);
            expect(physicsManager.stats.rigidBodiesCount).toBe(0);
            expect(physicsManager.stats.triggersCount).toBe(0);
        });

        test('应该设置默认配置', () => {
            expect(physicsManager.config.gravity).toEqual(new Vector3(0, -9.81, 0));
            expect(physicsManager.config.enableDebugDraw).toBe(false);
            expect(physicsManager.config.substeps).toBe(1);
            expect(physicsManager.config.maxSubsteps).toBe(4);
            expect(physicsManager.config.fixedTimeStep).toBe(1/60);
        });
    });

    describe('初始化', () => {
        test('应该成功初始化物理引擎', async () => {
            const result = await physicsManager.initialize();
            
            expect(result).toBe(true);
            expect(physicsManager.isInitialized).toBe(true);
            expect(mockScene.enablePhysics).toHaveBeenCalled();
        });

        test('应该处理初始化失败', async () => {
            // Mock 初始化失败
            mockScene.enablePhysics.mockImplementation(() => {
                throw new Error('初始化失败');
            });

            const result = await physicsManager.initialize();
            
            expect(result).toBe(false);
            expect(physicsManager.isInitialized).toBe(false);
        });

        test('应该合并自定义配置选项', async () => {
            const customOptions = {
                gravity: new Vector3(0, -5, 0),
                enableDebugDraw: true,
                maxSubsteps: 8
            };

            await physicsManager.initialize(customOptions);
            
            expect(physicsManager.config.gravity).toEqual(customOptions.gravity);
            expect(physicsManager.config.enableDebugDraw).toBe(true);
            expect(physicsManager.config.maxSubsteps).toBe(8);
        });
    });

    describe('状态检查', () => {
        test('isReady() 应该在未初始化时返回false', () => {
            expect(physicsManager.isReady()).toBe(false);
        });

        test('isReady() 应该在初始化后返回true', async () => {
            await physicsManager.initialize();
            expect(physicsManager.isReady()).toBe(true);
        });
    });

    describe('重力管理', () => {
        beforeEach(async () => {
            await physicsManager.initialize();
        });

        test('应该能够设置重力', () => {
            const newGravity = new Vector3(0, -15, 0);
            physicsManager.setGravity(newGravity);
            
            expect(physicsManager.config.gravity).toEqual(newGravity);
            expect(mockScene.getPhysicsEngine().setGravity).toHaveBeenCalledWith(newGravity);
        });

        test('应该能够获取当前重力', () => {
            const gravity = physicsManager.getGravity();
            expect(gravity).toEqual(new Vector3(0, -9.81, 0));
        });

        test('未初始化时设置重力应该显示警告', () => {
            const uninitializedManager = new PhysicsManager(mockScene);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            uninitializedManager.setGravity(new Vector3(0, -5, 0));
            
            expect(consoleSpy).toHaveBeenCalledWith('物理引擎未初始化，无法设置重力');
            consoleSpy.mockRestore();
        });
    });

    describe('物理形状创建', () => {
        beforeEach(async () => {
            await physicsManager.initialize();
        });

        test('应该能够创建盒子形状', () => {
            const shape = physicsManager.createPhysicsShape(PhysicsShapeTypes.BOX, {
                size: new Vector3(2, 2, 2),
                material: PhysicsMaterialPresets.WOOD
            });
            
            // 由于我们mock了Babylon.js，这里主要测试函数调用不出错
            expect(shape).toBeDefined();
        });

        test('应该能够创建球体形状', () => {
            const shape = physicsManager.createPhysicsShape(PhysicsShapeTypes.SPHERE, {
                radius: 1.5,
                material: PhysicsMaterialPresets.RUBBER
            });
            
            expect(shape).toBeDefined();
        });

        test('应该处理不支持的形状类型', () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            
            const shape = physicsManager.createPhysicsShape('INVALID_SHAPE');
            
            expect(shape).toBeNull();
            expect(consoleSpy).toHaveBeenCalledWith('不支持的物理形状类型: INVALID_SHAPE');
            consoleSpy.mockRestore();
        });

        test('未初始化时创建形状应该返回null', () => {
            const uninitializedManager = new PhysicsManager(mockScene);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            const shape = uninitializedManager.createPhysicsShape(PhysicsShapeTypes.BOX);
            
            expect(shape).toBeNull();
            expect(consoleSpy).toHaveBeenCalledWith('物理引擎未初始化，无法创建物理形状');
            consoleSpy.mockRestore();
        });
    });

    describe('射线检测', () => {
        beforeEach(async () => {
            await physicsManager.initialize();
        });

        test('应该能够执行射线检测', () => {
            const origin = new Vector3(0, 10, 0);
            const direction = new Vector3(0, -1, 0);
            const distance = 20;
            
            const result = physicsManager.raycast(origin, direction, distance);
            
            expect(mockScene.getPhysicsEngine().raycast).toHaveBeenCalled();
            expect(physicsManager.stats.raycastsPerFrame).toBe(1);
        });

        test('应该处理射线检测选项', () => {
            const origin = new Vector3(0, 10, 0);
            const direction = new Vector3(0, -1, 0);
            const options = {
                ignoreList: [],
                includeList: null
            };
            
            physicsManager.raycast(origin, direction, 20, options);
            
            expect(mockScene.getPhysicsEngine().raycast).toHaveBeenCalled();
        });

        test('未初始化时射线检测应该返回null', () => {
            const uninitializedManager = new PhysicsManager(mockScene);
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            
            const result = uninitializedManager.raycast(Vector3.Zero(), Vector3.Up());
            
            expect(result).toBeNull();
            expect(consoleSpy).toHaveBeenCalledWith('物理引擎未初始化，无法进行射线检测');
            consoleSpy.mockRestore();
        });
    });

    describe('统计信息', () => {
        test('应该能够获取统计信息', () => {
            const stats = physicsManager.getStats();
            
            expect(stats).toHaveProperty('rigidBodiesCount');
            expect(stats).toHaveProperty('triggersCount');
            expect(stats).toHaveProperty('raycastsPerFrame');
            expect(stats).toHaveProperty('isInitialized');
            expect(stats).toHaveProperty('gravity');
            expect(stats).toHaveProperty('physicsEngineType');
            expect(stats.physicsEngineType).toBe('Havok');
        });

        test('应该能够重置统计信息', () => {
            physicsManager.stats.raycastsPerFrame = 10;
            physicsManager.resetStats();
            
            expect(physicsManager.stats.raycastsPerFrame).toBe(0);
            expect(physicsManager.stats.lastUpdateTime).toBeGreaterThan(0);
        });
    });

    describe('系统控制', () => {
        beforeEach(async () => {
            await physicsManager.initialize();
        });

        test('应该能够暂停物理模拟', () => {
            physicsManager.pause();
            expect(mockScene.getPhysicsEngine().setEnabled).toHaveBeenCalledWith(false);
        });

        test('应该能够恢复物理模拟', () => {
            physicsManager.resume();
            expect(mockScene.getPhysicsEngine().setEnabled).toHaveBeenCalledWith(true);
        });

        test('应该能够设置调试绘制', () => {
            physicsManager.setDebugDraw(true);
            expect(physicsManager.config.enableDebugDraw).toBe(true);
        });
    });

    describe('资源清理', () => {
        test('应该能够清理所有资源', async () => {
            await physicsManager.initialize();
            
            physicsManager.dispose();
            
            expect(mockScene.disablePhysicsEngine).toHaveBeenCalled();
            expect(physicsManager.isInitialized).toBe(false);
            expect(physicsManager.physicsBodies.size).toBe(0);
            expect(physicsManager.triggers.size).toBe(0);
            expect(physicsManager.stats.rigidBodiesCount).toBe(0);
            expect(physicsManager.stats.triggersCount).toBe(0);
        });
    });
});

describe('物理材质预设', () => {
    test('应该包含所有预定义材质', () => {
        expect(PhysicsMaterialPresets.DEFAULT).toBeDefined();
        expect(PhysicsMaterialPresets.ICE).toBeDefined();
        expect(PhysicsMaterialPresets.RUBBER).toBeDefined();
        expect(PhysicsMaterialPresets.METAL).toBeDefined();
        expect(PhysicsMaterialPresets.WOOD).toBeDefined();
        expect(PhysicsMaterialPresets.STONE).toBeDefined();
        expect(PhysicsMaterialPresets.PLAYER).toBeDefined();
    });

    test('材质应该有正确的属性', () => {
        expect(PhysicsMaterialPresets.DEFAULT).toHaveProperty('friction');
        expect(PhysicsMaterialPresets.DEFAULT).toHaveProperty('restitution');
        expect(typeof PhysicsMaterialPresets.DEFAULT.friction).toBe('number');
        expect(typeof PhysicsMaterialPresets.DEFAULT.restitution).toBe('number');
    });
});

describe('物理形状类型', () => {
    test('应该包含所有支持的形状类型', () => {
        expect(PhysicsShapeTypes.BOX).toBe('box');
        expect(PhysicsShapeTypes.SPHERE).toBe('sphere');
        expect(PhysicsShapeTypes.CAPSULE).toBe('capsule');
        expect(PhysicsShapeTypes.CONVEX_HULL).toBe('convexHull');
        expect(PhysicsShapeTypes.MESH).toBe('mesh');
    });
});

console.log('PhysicsManager单元测试已加载');
