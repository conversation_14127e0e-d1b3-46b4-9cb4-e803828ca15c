// src/core/SceneConfig.js
// 场景配置定义 - 定义各种场景的配置信息和预设

/**
 * 默认场景配置
 * DefaultSceneConfigs对象的属性名只在本函数内使用, 似乎不是很有用(主要是其键容易造成歧义), DefaultSceneConfigs可以用数组替代
 * id: SceneManager中场景实例的索引键, 是惟一的.
 * type: SceneFactory中sceneTypes的key, type与创建场景实例的factory函数是一一对应的, 由于options不同, 同一type下可以有多个id对应的场景.
 * name: 场景名称
 * description: 场景描述
 * options: 场景选项, 会传给SceneFactory.createScene的options参数, 即sceneInfo.factory(options).
 * preload: 是否预加载
 * isDefault: 是否为默认场景
 */
export const DefaultSceneConfigs = {
    // 山海经世界场景（主游戏场景）
    shanhaijingWorld: {
        id: 'shanhaijing-main',
        type: 'shanhaijing',
        name: '山海经世界',
        description: '完整的山海经主题MMORPG游戏世界',
        options: {
            // 地形配置
            terrain: {
                type: 'mountains',
                seed: 55555,
                size: 512,
                heightScale: 50
            },
            // 环境配置
            environment: {
                enableDayNightCycle: true,
                dayDuration: 600000, // 10分钟
                initialTimeOfDay: 0.3, // 早晨
                enableWeather: true,
                initialWeather: 'clear'
            },
            // 玩家配置
            player: {
                spawnPosition: [0, 10, 0],
                enablePhysics: true,
                enableInput: true
            },
            // 相机配置
            camera: {
                type: 'arcRotate',
                distance: 30,
                minDistance: 5,
                maxDistance: 200,
                followPlayer: true
            }
        },
        preload: true, // 是否预加载
        isDefault: true // 是否为默认场景
    },

    // 默认纹理场景
    defaultTexture: {
        id: 'default-texture',
        type: 'default',
        name: '默认纹理场景',
        description: '基础的演示场景，包含球体和纹理地面',
        options: {
            enableShadows: true,
            lightIntensity: 0.5
        },
        preload: false,
        isDefault: false
    },

    // 菲涅尔着色器场景
    fresnelShader: {
        id: 'fresnel-shader',
        type: 'fresnel',
        name: '菲涅尔着色器场景',
        description: '展示菲涅尔着色器效果',
        options: {
            shaderQuality: 'high',
            enableReflection: true
        },
        preload: false,
        isDefault: false
    },

    // 模型环境场景
    modelEnvironment: {
        id: 'model-env',
        type: 'modelAndEnv',
        name: '模型环境场景',
        description: '加载3D模型和环境贴图',
        options: {
            modelPath: 'models/default.glb',
            environmentPath: 'environment/default.env',
            enablePBR: true
        },
        preload: false,
        isDefault: false
    },

    // 导航网格场景
    navigationMesh: {
        id: 'navigation-mesh',
        type: 'navigation',
        name: '导航网格场景',
        description: '使用Recast导航网格的AI寻路演示',
        options: {
            meshResolution: 0.2,
            agentRadius: 0.5,
            agentHeight: 2.0
        },
        preload: false,
        isDefault: false
    },

    // Havok物理场景
    physicsHavok: {
        id: 'physics-havok',
        type: 'physicsHavok',
        name: 'Havok物理场景',
        description: '使用Havok物理引擎的物理模拟',
        options: {
            gravity: [0, -9.81, 0],
            enableDebugDraw: false,
            substeps: 1
        },
        preload: false,
        isDefault: false
    },

    // Ammo物理场景
    physicsAmmo: {
        id: 'physics-ammo',
        type: 'physicsAmmo',
        name: 'Ammo物理场景',
        description: '使用Ammo.js物理引擎的物理模拟',
        options: {
            gravity: [0, -9.81, 0],
            enableDebugDraw: false
        },
        preload: false,
        isDefault: false
    }
};

/**
 * 场景切换配置
 */
export const SceneTransitionConfigs = {
    // 默认切换配置
    default: {
        enableTransition: true,
        fadeTime: 1000,
        fadeColor: [0, 0, 0, 1], // 黑色
        showLoadingScreen: true,
        loadingText: '正在加载场景...'
    },

    // 快速切换配置
    fast: {
        enableTransition: true,
        fadeTime: 300,
        fadeColor: [0, 0, 0, 1],
        showLoadingScreen: false
    },

    // 无过渡切换配置
    instant: {
        enableTransition: false,
        fadeTime: 0,
        showLoadingScreen: false
    },

    // 自定义过渡效果配置
    custom: {
        enableTransition: true,
        fadeTime: 800,
        fadeColor: [0.2, 0.3, 0.5, 1], // 蓝灰色
        showLoadingScreen: true,
        loadingText: '传送中...',
        customEffect: 'spiral' // 自定义效果类型
    }
};

/**
 * 场景预加载配置
 */
export const PreloadConfigs = {
    // 游戏启动时预加载的场景
    startup: [
        'shanhaijing-main', // 主游戏场景, 是DefaultSceneConfigs中value对象的id, 下面这些都是id
        'default-texture'   // 备用场景
    ],

    // 按需预加载的场景组
    groups: {
        // 演示场景组
        demo: [
            'fresnel-shader',
            'model-env',
            'navigation-mesh'
        ],

        // 物理场景组
        physics: [
            'physics-havok',
            'physics-ammo'
        ],

        // 测试场景组
        test: [
            'default-texture'
        ]
    },

    // 预加载策略
    strategy: {
        // 最大并发预加载数量
        maxConcurrent: 2,

        // 预加载优先级
        priority: {
            'shanhaijing-main': 10,
            'default-texture': 8,
            'fresnel-shader': 5,
            'model-env': 5,
            'navigation-mesh': 3,
            'physics-havok': 3,
            'physics-ammo': 3
        },

        // 内存限制（MB）
        memoryLimit: 512,

        // 是否在空闲时预加载
        preloadOnIdle: true
    }
};

/**
 * 获取默认场景配置
 * @returns {Object} 默认场景配置
 */
export function getDefaultSceneConfig() {
    return Object.values(DefaultSceneConfigs).find(config => config.isDefault)
        || DefaultSceneConfigs.shanhaijingWorld;
}

/**
 * 根据ID获取场景配置
 * @param {string} sceneId - 场景ID, DefaultSceneConfigs中value对象的id, 不是DefaultSceneConfigs的key
 * @returns {Object|null} 场景配置
 */
export function getSceneConfigById(sceneId) {
    return Object.values(DefaultSceneConfigs).find(config => config.id === sceneId) || null;
}

/**
 * 根据类型获取场景配置
 * @param {string} sceneType - 场景类型, DefaultSceneConfigs中value对象的type
 * @returns {Array} 场景配置数组
 */
export function getSceneConfigsByType(sceneType) {
    return Object.values(DefaultSceneConfigs).filter(config => config.type === sceneType);
}

/**
 * 获取所有场景配置
 * @returns {Array} 所有场景配置数组
 */
export function getAllSceneConfigs() {
    return Object.values(DefaultSceneConfigs);
}

/**
 * 创建自定义场景配置
 * @param {Object} baseConfig - 基础配置
 * @param {Object} overrides - 覆盖配置
 * @returns {Object} 合并后的配置
 */
export function createCustomSceneConfig(baseConfig, overrides = {}) {
    return {
        ...baseConfig,
        ...overrides,
        options: {
            ...baseConfig.options,
            ...overrides.options
        }
    };
}

/**
 * 验证场景配置
 * @param {Object} config - 场景配置
 * @returns {boolean} 是否有效
 */
export function validateSceneConfig(config) {
    if (!config || typeof config !== 'object') {
        return false;
    }

    // 必需字段检查
    const requiredFields = ['id', 'type', 'name'];
    for (const field of requiredFields) {
        if (!config[field]) {
            console.error(`场景配置缺少必需字段: ${field}`);
            return false;
        }
    }

    // ID格式检查
    if (!/^[a-zA-Z0-9-_]+$/.test(config.id)) {
        console.error(`场景ID格式无效: ${config.id}`);
        return false;
    }

    return true;
}

export default {
    DefaultSceneConfigs,
    SceneTransitionConfigs,
    PreloadConfigs,
    getDefaultSceneConfig,
    getSceneConfigById,
    getSceneConfigsByType,
    getAllSceneConfigs,
    createCustomSceneConfig,
    validateSceneConfig
};
