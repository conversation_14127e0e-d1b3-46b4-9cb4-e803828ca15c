// src/world/terrainGenerator.js
// 地形生成器 - 负责生成程序化地形

import {
    Scene,
    Vector3,
    Color3,
    StandardMaterial,
    Texture,
    PhysicsBody,
    PhysicsMotionType,
    PhysicsShapeBox,
    Quaternion,
    DynamicTexture,
    MeshBuilder
} from "@babylonjs/core";

import { TerrainConfig, TerrainLODConfig, TerrainPresets } from "../config/terrain.js";

/**
 * 简化的柏林噪声实现
 * 用于生成自然的地形高度数据
 */
class SimplexNoise {
    constructor(seed = 12345) {
        this.seed = seed;
        this.perm = this.generatePermutation();
    }

    generatePermutation() {
        const p = [];
        for (let i = 0; i < 256; i++) {
            p[i] = i;
        }

        // 使用种子进行洗牌
        let seed = this.seed;
        for (let i = 255; i > 0; i--) {
            seed = (seed * 9301 + 49297) % 233280;
            const j = Math.floor((seed / 233280) * (i + 1));
            [p[i], p[j]] = [p[j], p[i]];
        }

        // 扩展到512长度
        for (let i = 0; i < 256; i++) {
            p[256 + i] = p[i];
        }

        return p;
    }

    fade(t) {
        return t * t * t * (t * (t * 6 - 15) + 10);
    }

    lerp(t, a, b) {
        return a + t * (b - a);
    }

    grad(hash, x, y) {
        const h = hash & 15;
        const u = h < 8 ? x : y;
        const v = h < 4 ? y : h === 12 || h === 14 ? x : 0;
        return ((h & 1) === 0 ? u : -u) + ((h & 2) === 0 ? v : -v);
    }

    noise(x, y) {
        const X = Math.floor(x) & 255;
        const Y = Math.floor(y) & 255;

        x -= Math.floor(x);
        y -= Math.floor(y);

        const u = this.fade(x);
        const v = this.fade(y);

        const A = this.perm[X] + Y;
        const AA = this.perm[A];
        const AB = this.perm[A + 1];
        const B = this.perm[X + 1] + Y;
        const BA = this.perm[B];
        const BB = this.perm[B + 1];

        return this.lerp(v,
            this.lerp(u, this.grad(this.perm[AA], x, y),
                         this.grad(this.perm[BA], x - 1, y)),
            this.lerp(u, this.grad(this.perm[AB], x, y - 1),
                         this.grad(this.perm[BB], x - 1, y - 1))
        );
    }

    octaveNoise(x, y, octaves, persistence, lacunarity, scale) {
        let value = 0;
        let amplitude = 1;
        let frequency = scale;
        let maxValue = 0;

        for (let i = 0; i < octaves; i++) {
            value += this.noise(x * frequency, y * frequency) * amplitude;
            maxValue += amplitude;
            amplitude *= persistence;
            frequency *= lacunarity;
        }

        return value / maxValue;
    }
}

/**
 * 地形生成器类
 * 负责生成程序化地形网格和材质
 */
export class TerrainGenerator {
    /**
     * 构造函数
     * @param {Scene} scene - Babylon.js场景实例
     * @param {Object} options - 地形生成选项
     */
    constructor(scene, options = {}) {
        this.scene = scene;
        this.options = { ...TerrainConfig.default, ...options };

        // 初始化噪声生成器
        this.noise = new SimplexNoise(this.options.noiseSettings.seed);

        // 存储生成的地形数据
        this.heightMapData = null;
        this.terrainMesh = null;
        this.terrainMaterial = null;

        console.log("地形生成器已初始化", this.options);
    }

    /**
     * 生成高度图数据
     * @param {number} seed - 随机种子
     * @returns {Float32Array} 高度图数据
     */
    generateHeightMap(seed = null) {
        if (seed !== null) {
            this.noise = new SimplexNoise(seed);
        }

        const { width, height } = this.options.size;
        const { subdivisions } = this.options;
        const { minHeight, maxHeight } = this.options.heightRange;
        const noiseSettings = this.options.noiseSettings;

        // 创建高度图数据数组
        const heightData = new Float32Array((subdivisions + 1) * (subdivisions + 1));

        console.log(`开始生成 ${subdivisions}x${subdivisions} 高度图...`);

        // 生成高度数据
        for (let z = 0; z <= subdivisions; z++) {
            for (let x = 0; x <= subdivisions; x++) {
                // 将网格坐标转换为世界坐标
                const worldX = (x / subdivisions - 0.5) * width;
                const worldZ = (z / subdivisions - 0.5) * height;

                // 生成多层噪声
                const noiseValue = this.noise.octaveNoise(
                    worldX,
                    worldZ,
                    noiseSettings.octaves,
                    noiseSettings.persistence,
                    noiseSettings.lacunarity,
                    noiseSettings.scale
                );

                // 将噪声值映射到高度范围
                const normalizedHeight = (noiseValue + 1) * 0.5; // 从[-1,1]映射到[0,1]
                const height = minHeight + normalizedHeight * (maxHeight - minHeight);

                heightData[z * (subdivisions + 1) + x] = height;
            }
        }

        this.heightMapData = heightData;
        console.log("高度图生成完成");
        return heightData;
    }

    /**
     * 创建地形网格
     * @param {Float32Array} heightMapData - 高度图数据
     * @returns {Mesh} 地形网格
     */
    createTerrain(heightMapData = null) {
        if (!heightMapData && !this.heightMapData) {
            console.warn("未提供高度图数据，将生成默认高度图");
            this.generateHeightMap();
        }

        const heightData = heightMapData || this.heightMapData;
        const { width, height } = this.options.size;
        const { subdivisions } = this.options;

        console.log("开始创建地形网格...");

        // 直接创建地形网格，不使用复杂的高度图纹理
        this.terrainMesh = MeshBuilder.CreateGround(
            "terrain",
            {
                width: width,
                height: height,
                subdivisions: subdivisions
            },
            this.scene
        );

        // 应用高度数据到顶点
        this.applyHeightDataToMesh(this.terrainMesh, heightData);

        console.log("地形网格创建完成");
        this.setupTerrainPhysics(this.terrainMesh);

        return this.terrainMesh;
    }

    /**
     * 应用高度数据到网格顶点
     * @param {Mesh} mesh - 地形网格
     * @param {Float32Array} heightData - 高度数据
     */
    applyHeightDataToMesh(mesh, heightData) {
        const positions = mesh.getVerticesData("position");
        const { subdivisions } = this.options;

        // 更新Y坐标（高度）
        for (let i = 0; i < positions.length; i += 3) {
            const vertexIndex = Math.floor(i / 3);
            const row = Math.floor(vertexIndex / (subdivisions + 1));
            const col = vertexIndex % (subdivisions + 1);
            const heightIndex = row * (subdivisions + 1) + col;

            if (heightIndex < heightData.length) {
                positions[i + 1] = heightData[heightIndex]; // Y坐标
            }
        }

        // 更新网格顶点数据
        mesh.setVerticesData("position", positions);
        mesh.createNormals(true); // 重新计算法线
    }

    /**
     * 设置地形物理属性
     * @param {Mesh} terrainMesh - 地形网格
     */
    setupTerrainPhysics(terrainMesh) {
        console.log("设置地形物理属性...");

        // 创建地形物理体
        const terrainPhysicsBody = new PhysicsBody(
            terrainMesh,
            PhysicsMotionType.STATIC,
            false,
            this.scene
        );

        // 创建地形碰撞形状（使用简化的盒子形状以提高性能）
        const { width, height } = this.options.size;
        const { minHeight, maxHeight } = this.options.heightRange;
        const avgHeight = (minHeight + maxHeight) * 0.5;

        const terrainShape = new PhysicsShapeBox(
            new Vector3(0, avgHeight * 0.5, 0),
            new Quaternion(0, 0, 0, 1),
            new Vector3(width, avgHeight, height),
            this.scene
        );

        // 设置物理材质属性
        terrainShape.material = {
            friction: this.options.physics.friction,
            restitution: this.options.physics.restitution
        };

        terrainPhysicsBody.shape = terrainShape;
        terrainPhysicsBody.setMassProperties({ mass: 0 });

        console.log("地形物理属性设置完成");
    }

    /**
     * 应用地形材质
     * @param {Mesh} terrainMesh - 地形网格
     * @returns {Material} 地形材质
     */
    applyTerrainMaterial(terrainMesh = null) {
        const mesh = terrainMesh || this.terrainMesh;
        if (!mesh) {
            console.error("未找到地形网格，无法应用材质");
            return null;
        }

        console.log("开始应用地形材质...");

        // 创建标准材质（简化版，不使用复杂的混合材质）
        this.terrainMaterial = new StandardMaterial("terrainMaterial", this.scene);

        const materials = this.options.materials;
        const materialKeys = Object.keys(materials);

        // 使用简化的材质设置
        if (materialKeys.length > 0) {
            const firstMaterialKey = materialKeys[0];
            const materialConfig = materials[firstMaterialKey];

            // 创建漫反射纹理
            const diffuseTexture = this.createFallbackTexture(firstMaterialKey, materialConfig);
            this.terrainMaterial.diffuseTexture = diffuseTexture;
        } else {
            // 如果没有配置材质，使用基础颜色
            this.terrainMaterial.diffuseColor = this.createHeightBasedColor();
        }

        // 设置材质属性
        this.terrainMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
        this.terrainMaterial.roughness = 0.8;

        // 应用材质到网格
        mesh.material = this.terrainMaterial;

        console.log("地形材质应用完成");
        return this.terrainMaterial;
    }

    /**
     * 创建后备纹理（程序化生成）
     * @param {string} materialType - 材质类型
     * @param {Object} config - 材质配置
     * @returns {DynamicTexture} 程序化纹理
     */
    createFallbackTexture(materialType, config) {
        const textureSize = 256;
        const texture = new DynamicTexture(
            `${materialType}Texture`,
            { width: textureSize, height: textureSize },
            this.scene,
            false
        );

        const context = texture.getContext();

        // 根据材质类型生成不同的程序化纹理
        switch (materialType) {
            case 'grass':
                this.generateGrassTexture(context, textureSize);
                break;
            case 'rock':
                this.generateRockTexture(context, textureSize);
                break;
            case 'snow':
                this.generateSnowTexture(context, textureSize);
                break;
            default:
                this.generateDefaultTexture(context, textureSize);
        }

        texture.update();
        return texture;
    }

    /**
     * 生成草地纹理
     */
    generateGrassTexture(context, size) {
        context.fillStyle = '#2d5016';
        context.fillRect(0, 0, size, size);

        // 添加草地细节
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * size;
            const y = Math.random() * size;
            const shade = Math.random() * 0.3 + 0.7;
            context.fillStyle = `rgba(${Math.floor(45 * shade)}, ${Math.floor(80 * shade)}, ${Math.floor(22 * shade)}, 1)`;
            context.fillRect(x, y, 2, 2);
        }
    }

    /**
     * 生成岩石纹理
     */
    generateRockTexture(context, size) {
        context.fillStyle = '#666666';
        context.fillRect(0, 0, size, size);

        // 添加岩石细节
        for (let i = 0; i < 500; i++) {
            const x = Math.random() * size;
            const y = Math.random() * size;
            const shade = Math.random() * 0.4 + 0.6;
            context.fillStyle = `rgba(${Math.floor(102 * shade)}, ${Math.floor(102 * shade)}, ${Math.floor(102 * shade)}, 1)`;
            context.fillRect(x, y, 3, 3);
        }
    }

    /**
     * 生成雪地纹理
     */
    generateSnowTexture(context, size) {
        context.fillStyle = '#f0f0f0';
        context.fillRect(0, 0, size, size);

        // 添加雪地细节
        for (let i = 0; i < 300; i++) {
            const x = Math.random() * size;
            const y = Math.random() * size;
            const shade = Math.random() * 0.2 + 0.8;
            context.fillStyle = `rgba(${Math.floor(240 * shade)}, ${Math.floor(240 * shade)}, ${Math.floor(240 * shade)}, 1)`;
            context.fillRect(x, y, 2, 2);
        }
    }

    /**
     * 生成默认纹理
     */
    generateDefaultTexture(context, size) {
        context.fillStyle = '#8B4513';
        context.fillRect(0, 0, size, size);
    }

    /**
     * 创建基于高度的颜色材质（简化版）
     * @returns {Color3} 基础颜色
     */
    createHeightBasedColor() {
        // 简化版：返回基础的绿色作为草地颜色
        // 在未来版本中可以实现更复杂的高度基础颜色混合
        return new Color3(0.3, 0.6, 0.2); // 草绿色
    }

    /**
     * 生成完整地形
     * @param {string} presetName - 预设名称
     * @param {number} seed - 随机种子
     * @returns {Mesh} 生成的地形网格
     */
    generateTerrain(presetName = null, seed = null) {
        console.log("开始生成完整地形...");

        // 应用预设配置
        if (presetName && TerrainPresets[presetName]) {
            this.options = { ...this.options, ...TerrainPresets[presetName] };
            console.log(`应用地形预设: ${presetName}`);
        }

        // 生成高度图
        this.generateHeightMap(seed);

        // 创建地形网格
        this.createTerrain();

        // 应用材质
        this.applyTerrainMaterial();

        console.log("地形生成完成");
        return this.terrainMesh;
    }

    /**
     * 释放资源
     */
    dispose() {
        if (this.terrainMesh) {
            this.terrainMesh.dispose();
            this.terrainMesh = null;
        }

        if (this.terrainMaterial) {
            this.terrainMaterial.dispose();
            this.terrainMaterial = null;
        }

        this.heightMapData = null;
        console.log("地形生成器资源已释放");
    }
}

export default TerrainGenerator;
